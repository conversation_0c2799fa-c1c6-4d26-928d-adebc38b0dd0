# .github/workflows/pytest.yml
name: PyTest
on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      # Setup Python (faster than using Python container)
      - name: Setup Python
        uses: actions/setup-python@v3
        with:
          python-version: "3.8"

      # Install pip dependencies + setup for testing
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          python -m spacy download en_core_web_lg

      # Run testing suite
      - name: Run test suite
        run: |
          pytest -v