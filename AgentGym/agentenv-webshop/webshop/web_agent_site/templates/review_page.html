<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" href="{{url_for('static', filename='style.css')}}">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css	">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
    <link rel="icon" href="data:,">
  </head>
  <body>
    <div class="container py-5">
      <div class="row top-buffer">
        <div class="col-sm-6">
          <div id="instruction-text" class="text-center">
            <h4>Instruction:<br>{{ instruction_text }}</h4>
          </div>
        </div>
      </div>
      <div class="row top-buffer">
        <form method="post" action="{{url_for('index', session_id=session_id)}}">
          <button type="submit" class="btn btn-success">Back to Search</button>
        </form>
      </div>
      <div class="row top-buffer">
        <form method="post" action="{{url_for('item_page', session_id=session_id, asin=asin, keywords=keywords, page=page, options=options)}}">
          <button type="submit" class="btn btn-primary">&lt; Prev</button>
        </form>
      </div>
      <div class="row top-buffer">
        <div class="col-md-12">
          <div class="row top-buffer">
            <div class="col-sm-6" name="reviews">
              <div class="card card-body">
                {% for review in product_info.Reviews %}
                  <div class="card">
                    <div class="row text-left">
                      <h4 class="blue-text mt-3">"{{review.title}}"</h4>
                      <p class="text-left">
                        <span>{{review.score}}</span>
                        {% for i in range(review.score | int) %}
                            <span class="fa fa-star star-active"></span>
                        {% endfor %}
                        {% for i in range(5 - review.score | int) %}
                            <span class="fa fa-star star-inactive"></span>
                        {% endfor %}
                      </p>
                      <p class="content">{{review.body}}</p>
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>