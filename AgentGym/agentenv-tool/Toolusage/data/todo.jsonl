{"id": 0, "goal": "When is the due date of Solve algebra equations in Homework and Assignments? Please provide the answer in the YYYY-MM-DD format. ", "subgoals": ["2015-06-01"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 1, "goal": "Finally, I have completed the task Read and summarize a history chapter, please mark it as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 2, "goal": "I think it is hard for me to complete the task Conduct a chemistry experiment timely, please extend the due date to 2015-06-30 ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-30"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 3, "goal": "Which task should I do first today to avoid missing the deadline? Solve algebra equations or Conduct a chemistry experiment ? ", "subgoals": ["Solve algebra equations"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 4, "goal": "Which task should I do first today to avoid missing the deadline? Solve algebra equations or Attend soccer practice ? ", "subgoals": ["Solve algebra equations"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 5, "goal": "What tasks should I complete before 2015-06-11 in Homework and Assignments project ? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Read and summarize a history chapter", "Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 6, "goal": "What tasks should I complete before 2015-06-11? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting", "Read and summarize a history chapter", "Rehearse with the school band", "Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-05-31", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 7, "goal": "Until today, how many tasks are overdue? Please give me a number as an answer. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-03", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 8, "goal": "What is the most important task for me in the next 7 days ? ", "subgoals": ["Rehearse with the school band"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 9, "goal": "How long is it expected to take to complete the task Rehearse with the school band? Please answer in the format of 'number(unit)'. ", "subgoals": ["60(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 10, "goal": "I recall the task of summarizing a history chapter, but I've forgotten the chapter's title. Could you please remind me of the chapter's name? ", "subgoals": ["World Modern History"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 11, "goal": "Is the location for conducting chemistry experiments the same as the location for rehearsing with the school band? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 12, "goal": "Until today, some tasks have expired. Please help me adjust their deadlines to tomorrow. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-06"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-06"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 13, "goal": "Until today, please help me clear all expired tasks ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 14, "goal": "Which task requires more time, attending soccer practice or rehearsing with the school band? ", "subgoals": ["attending soccer practice"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 15, "goal": "Please mark the project that includes the chemistry experiment task as a favorite. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": true}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 16, "goal": "What is the task that takes the longest time in the Homework and Assignments project? ", "subgoals": ["Conduct a chemistry experiment"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 17, "goal": "Which task in the Extracurricular Activities project requires the least amount of time? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting", "Rehearse with the school band"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 18, "goal": "How many tasks are there in total? Please answer in the form of a number. ", "subgoals": [17], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 19, "goal": "What is the priority level of the task \"Write a research paper on the experiment\" in the Science Fair Project? Please answer in the form of a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 20, "goal": "How many minutes in total are required to complete all tasks in the Picnic Preparation project? Please answer in the form of a number. ", "subgoals": [105], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 21, "goal": "What is the due date for the task \"Select picnic location and plan activities\" in the Picnic Preparation project? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-25"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 22, "goal": "Which task in the Homework and Assignments project has the longest duration? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Conduct a chemistry experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 23, "goal": "What is the description of the task \"Do the laundry\" in the Household Chores project? Please answer in the form of a string. ", "subgoals": ["- Place: Home\n- Tasks: Sort clothes, wash, dry, fold, and put away\n- Tips: Separate whites, colors, and delicates to avoid color bleeding or damage."], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 24, "goal": "Is there any task with a priority level of 4 (urgent)? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 25, "goal": "What is the due date for the task \"Prepare presentation for science fair\"? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-21"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 26, "goal": "How many projects are there? Please answer in the form of a number. ", "subgoals": [5], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 27, "goal": "What is the project name that includes the task \"Tidy up the living room\"? Please answer in the form of a string. ", "subgoals": ["Household Chores"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 28, "goal": "Which project is marked as a favorite? Please answer in the form of a string or \"None\".", "subgoals": ["None"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 29, "goal": "How many tasks are overdue? Please answer in the form of a number. ", "subgoals": [2], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 30, "goal": "What is the description of the task \"Prepare presentation for science fair\"? Please answer in the form of a string. ", "subgoals": ["- Place: Home\n- Format: PowerPoint presentation\n- Content: Introduction, hypothesis, methodology, results, conclusion, and references\n- Tips: Practice presenting to family members or friends to improve public speaking skills."], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 31, "goal": "Which task has the highest priority level? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend soccer practice", "Rehearse with the school band", "Water the plants"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 32, "goal": "Are there any tasks that require more than 1 hour to complete? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 33, "goal": "What is the due date for the task \"Prepare presentation for science fair\"? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-21"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 34, "goal": "How many tasks have a priority level of 2? Please answer in the form of a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 35, "goal": "What is the duration of the task \"Water the plants\" in the Household Chores project? Please answer in the format 'number(unit)'. ", "subgoals": ["20(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 36, "goal": "Which task has the earliest due date? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 37, "goal": "What is the location for conducting the task \"Design and conduct a biology experiment\"? Please answer in the form of a string. ", "subgoals": ["School laboratory"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 38, "goal": "Are there any tasks with a duration of more than 2 hours? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 39, "goal": "Which task has the highest priority level? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend soccer practice", "Rehearse with the school band", "Water the plants"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 40, "goal": "What is the due date for the task \"Attend soccer practice\"? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-12"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 41, "goal": "How many tasks have a priority level of 1? Please answer in the form of a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 42, "goal": "Which project has the most tasks? Please answer in the form of a string. ", "subgoals": ["Household Chores"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 43, "goal": "What is the duration of the task \"Clean the kitchen\" in the Household Chores project? Please answer in the format 'number(unit)'. ", "subgoals": ["45(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 44, "goal": "What tasks need to be completed before 2015-06-11 in the Homework and Assignments project? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Read and summarize a history chapter", "Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 45, "goal": "What tasks need to be completed before 2015-06-11? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting", "Read and summarize a history chapter", "Rehearse with the school band", "Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 46, "goal": "Until today, how many tasks are overdue? Please provide the answer as a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-03", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 47, "goal": "How long is it expected to take to complete the task \"Rehearse with the school band\"? Please answer in the format of 'number(unit)'. ", "subgoals": ["60(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 48, "goal": "I recall the task of summarizing a history chapter, but I've forgotten the chapter's title. Could you please remind me of the chapter's name? Please answer in the form of a string. ", "subgoals": ["World Modern History"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 49, "goal": "Until today, some tasks have expired. Please help me adjust their deadlines to tomorrow. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-06"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-06"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 50, "goal": "Until today, please help me clear all expired tasks. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 51, "goal": "Which task requires more time, attending soccer practice or rehearsing with the school band? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend soccer practice"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 52, "goal": "Please mark the project that includes the chemistry experiment task as a favorite. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": true}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 53, "goal": "I remember there is an assignment that required me to contact the teacher in advance. Can you help me find out which task it is? Please answer in the form of a string. ", "subgoals": ["Solve algebra equations"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 54, "goal": "What tasks need to be completed in the Student Building according to the to-do list? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [[]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 55, "goal": "What is the task that takes the longest time in the Homework and Assignments project? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Conduct a chemistry experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 56, "goal": "Which tasks have the same deadline as attending soccer practice? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Conduct a chemistry experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 57, "goal": "What is the project name that includes the task \"Attend soccer practice\"? Please answer in the form of a string. ", "subgoals": ["Extracurricular Activities"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 58, "goal": "What tasks need to be completed before the due date of \"Attend soccer practice\"? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting", "Rehearse with the school band"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 59, "goal": "Which task has the earliest due date among all projects? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 60, "goal": "What is the total duration of tasks in the \"Extracurricular Activities\" project? Please answer in the format 'number(unit)'. ", "subgoals": ["210(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 61, "goal": "What tasks in the Science Fair Project have a duration of more than 1 hour? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Design and conduct a biology experiment", "Prepare presentation for science fair", "Write a research paper on the experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 62, "goal": "What tasks have a priority level of 3? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Do the laundry", "Prepare presentation for science fair", "Read and summarize a history chapter", "Select picnic location and plan activities"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 63, "goal": "How many tasks are there in the \"Picnic Preparation\" project? Please answer in the form of a number. ", "subgoals": [2], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 64, "goal": "What is the due date for the task \"Solve algebra equations\" in the Homework and Assignments project? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-01"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 65, "goal": "Are there any tasks in the Household Chores project with a priority level of 4 (urgent)? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 66, "goal": "What is the total duration of all tasks in the \"Extracurricular Activities\" project? Please answer in the format 'number(unit)'. ", "subgoals": ["210(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 67, "goal": "What is the project name that includes the task \"Write a research paper on the experiment\"? Please answer in the form of a string. ", "subgoals": ["Science Fair Project"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 68, "goal": "What tasks in the Homework and Assignments project need to be completed before the due date of \"Conduct a chemistry experiment\"? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Read and summarize a history chapter", "Solve algebra equations"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 69, "goal": "Which project has the most urgent task based on priority level? Please answer in the form of a string. ", "subgoals": ["Extracurricular Activities"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 70, "goal": "What is the description of the task \"Conduct a chemistry experiment\" in the Homework and Assignments project? Please answer in the form of a string. ", "subgoals": ["- Place: Student Building\n- Textbook: Principles of Modern Chemistry by Dr. Alan Thompson\n- Equipment Needed: pH paper, thermometer and stirring rod\n- Chemicals Required: distilled water, hydrochloric acid and sodium hydroxide\n- Tips: 1. Ensure all apparatus are clean and dry before starting. 2. Conduct the experiment slowly to ensure accuracy. 3. Contact the teacher in advance."], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 71, "goal": "Is there any task in the Household Chores project that requires more than 1 hour to complete? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 72, "goal": "How many tasks in the \"Extracurricular Activities\" project have a priority level of 1? Please answer in the form of a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 73, "goal": "What is the due date for the task \"Prepare presentation for science fair\"? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-21"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 74, "goal": "What tasks in the Picnic Preparation project have a priority level of 2? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Purchase picnic supplies"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 75, "goal": "How many tasks in total need to be completed before 2015-06-11? Please answer in the form of a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 76, "goal": "What is the priority level of the task \"Attend soccer practice\" in the Extracurricular Activities project? Please answer in the form of a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 77, "goal": "Are there any tasks in the Picnic Preparation project with a priority level of 3? Please answer Yes or No. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 78, "goal": "What tasks in the Science Fair Project have a due date before 2015-06-20? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Design and conduct a biology experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 79, "goal": "What's the current status of the task \"Prepare presentation for science fair\"? ", "subgoals": ["The task 'Prepare presentation for science fair' is not completed."], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 80, "goal": "How many tasks are there in the \"Science Fair Project\" project? ", "subgoals": [3], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 81, "goal": "Please adjust the due date for the task \"Prepare presentation for science fair\" to 2015-06-28. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-28"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 82, "goal": "Could you mark the project \"Extracurricular Activities\" as a favorite? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": true}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 83, "goal": "Extend the due date for the task \"Tidy up the living room\" to 2015-06-26. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-26"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 84, "goal": "Could you update the due date for the task \"Attend debate club meeting\" to 2015-06-15? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-15"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 85, "goal": "Please delete the task \"Water the plants\". ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 86, "goal": "Mark the task \"Design and conduct a biology experiment\" as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 87, "goal": "Could you update the due date for the task \"Clean the kitchen\" to 2015-06-25? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-25"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 88, "goal": "Delete the task \"Rehearse with the school band\". ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 89, "goal": "Extend the due date for the task \"Attend soccer practice\" to 2015-06-15. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-15"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 90, "goal": "Mark the task \"Read and summarize a history chapter\" as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 91, "goal": "Could you update the due date for the task \"Do the laundry\" to 2015-06-28? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-28"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 92, "goal": "Please adjust the due date for the task \"Write a research paper on the experiment\" to 2015-06-30. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-30"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 93, "goal": "Delete the task \"Solve algebra equations\". ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 94, "goal": "Mark the task \"Select picnic location and plan activities\" as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 95, "goal": "Please update the due date for the task \"Conduct a chemistry experiment\" to 2015-07-10. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-07-10"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 96, "goal": "Could you mark the project \"Homework and Assignments\" as a favorite? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": true}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 97, "goal": "Which task in the Extracurricular Activities project requires the least amount of time? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting", "Rehearse with the school band"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 98, "goal": "What is the priority level of the task 'Write a research paper on the experiment' in the Science Fair Project project? Please answer as a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 99, "goal": "Among the tasks in the Household Chores project, which one has the highest priority? Please answer in the form of a list ['task1', 'task2', ...].", "subgoals": [["Water the plants"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 100, "goal": "Could you provide the due dates for both 'Prepare presentation for science fair' and 'Attend debate club meeting'? Please answer in the format of a list ['YYYY-MM-DD', 'YYYY-MM-DD']. ", "subgoals": [["2015-06-08", "2015-06-21"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 101, "goal": "What is the total duration needed to complete all tasks in the Picnic Preparation project? Please answer in the format of 'number(unit)'. ", "subgoals": ["105(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 102, "goal": "Is there any task in the Extracurricular Activities project that needs to be completed before the task 'Attend soccer practice'? Please answer 'Yes' or 'No'. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 103, "goal": "What is the total number of tasks in the Todoist account? Please answer as a number. ", "subgoals": [15], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 104, "goal": "Could you provide the due date for the task 'Tidy up the living room' in the Household Chores project? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-24"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 105, "goal": "Which project has the highest number of tasks? Please answer with the project name. ", "subgoals": ["Household Chores"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 106, "goal": "What is the priority level of the task 'Attend debate club meeting' in the Extracurricular Activities project? Please answer as a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 107, "goal": "Are there any tasks in the Todoist account with the same priority as 'Prepare presentation for science fair'? Please answer 'Yes' or 'No'. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 108, "goal": "Could you provide the due dates for all tasks in the Homework and Assignments project? Please answer in the format of a list ['YYYY-MM-DD', 'YYYY-MM-DD', ...]. ", "subgoals": [["2015-06-01", "2015-06-06", "2015-06-12"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 109, "goal": "Is there any task in the Todoist account that needs to be completed before the task 'Purchase picnic supplies' in the Picnic Preparation project? Please answer 'Yes' or 'No'. ", "subgoals": ["No"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 110, "goal": "Are there any tasks in the Todoist account with the same due date as 'Select picnic location and plan activities' in the Picnic Preparation project? Please answer 'Yes' or 'No'. ", "subgoals": ["No"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 111, "goal": "Could you provide the due date for the task 'Attend soccer practice' in the Extracurricular Activities project? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-12"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 112, "goal": "Among the tasks in the Extracurricular Activities project, which one has the earliest due date? Please answer with the task name. ", "subgoals": ["Rehearse with the school band"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 113, "goal": "Is there any task in the Todoist account that needs to be completed before the task 'Read and summarize a history chapter' in the Homework and Assignments project? Please answer 'Yes' or 'No'. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 114, "goal": "What is the total number of tasks with a priority level of 4 in the Todoist account? Please answer as a number. ", "subgoals": [3], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 115, "goal": "Could you provide the due date for the task 'Do the laundry' in the Household Chores project? Please answer in 'YYYY-MM-DD' format. ", "subgoals": ["2015-06-22"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 116, "goal": "Among the tasks in the Homework and Assignments project, which one has the latest due date? Please answer with the task name. ", "subgoals": ["Conduct a chemistry experiment"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 117, "goal": "What is the total duration needed to complete all tasks with a priority level of 2 in the Todoist account? Please answer in the format of 'number(unit)'. ", "subgoals": ["300(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 118, "goal": "Are there any tasks in the Todoist account with the same duration as 'Conduct a chemistry experiment' in the Homework and Assignments project? Please answer 'Yes' or 'No'. ", "subgoals": ["Yes"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 119, "goal": "Could you update the due date of the task \"Prepare presentation for science fair\" to three days later in the \"Science Fair Project\" project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-24"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-18", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 120, "goal": "Can you mark the task \"Tidy up the living room\" in the \"Household Chores\" project as completed? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-23", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 121, "goal": "Please help me delete the task \"Attend debate club meeting\" from the \"Extracurricular Activities\" project. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 122, "goal": "Could you update the due date of the task \"Write a research paper on the experiment\" to be one day earlier in the \"Science Fair Project\" project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-22"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-22", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 123, "goal": "Can you make the project \"Science Fair Project\" a favorite project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": true}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-19", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 124, "goal": "Please update the due date of the task \"Read and summarize a history chapter\" to two days later in the \"Homework and Assignments\" project. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-08"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-08", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 125, "goal": "Could you mark the task \"Attend soccer practice\" in the \"Extracurricular Activities\" project as completed? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-13", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 126, "goal": "Can you delete the task \"Clean the kitchen\" from the \"Household Chores\" project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-20", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 127, "goal": "Please update the due date of the task \"Rehearse with the school band\" to be tomorrow in the \"Extracurricular Activities\" project. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-01", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 128, "goal": "Can you rearrange the tasks in the \"Science Fair Project\" project by their priorities, from highest to lowest? ", "subgoals": [["Design and conduct a biology experiment", "Prepare presentation for science fair", "Write a research paper on the experiment"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-19", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 129, "goal": "Please mark the task \"Design and conduct a biology experiment\" in the \"Science Fair Project\" project as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-21", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 130, "goal": "Could you update the due date of the task \"Do the laundry\" to be two days earlier in the \"Household Chores\" project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-20"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-20", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 131, "goal": "Can you delete the task \"Select picnic location and plan activities\" from the \"Picnic Preparation\" project? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}]}, "init_config": {"current_date": "2015-06-26", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 132, "goal": "Could you mark the task \"Rehearse with the school band\" in the \"Extracurricular Activities\" project as completed? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-03", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 133, "goal": "Please help me expire tasks in the \"Science Fair Project\" project whose priority is 1. Then tell me how long is it expected to take to complete the all the tasks in \"Science Fair Project\" project? Please answer in the format of 'number(unit)'. ", "subgoals": ["330(minute)"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-05"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 134, "goal": "Until today, please help me clear all expired tasks. Then tell me what is the total duration of tasks in Homework and Assignments? Please answer as a number. ", "subgoals": [135], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-03", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 135, "goal": "Please help me mark all tasks in the \"Extracurricular Activities\" project with priority 1 as completed. Then, could you provide me with the names of those completed tasks? Please answer in the form of a list ['task1', 'task2', ...]. ", "subgoals": [["Attend debate club meeting"]], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 136, "goal": "Could you assist me in updating the due dates of all tasks in the \"Household Chores\" project to be one day earlier? Afterward, please tell me how many tasks are there in total across all projects. Please answer as a number. ", "subgoals": [15], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-19"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-23"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 137, "goal": "I need help in marking all tasks in the \"Picnic Preparation\" project with a due date before \"2015-06-23\" as completed. Then, could you tell me the total number of completed tasks across all projects? Please answer as a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-22", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 138, "goal": "Could you help me in completing all tasks in the \"Science Fair Project\" project? After that, please tell me the total number of tasks with priority 2 across all projects. Please answer as a number. ", "subgoals": [3], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-20", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 139, "goal": "Please help me update the due dates of all tasks in the \"Extracurricular Activities\" project to be two days later. Then, could you tell me the total number of tasks with priority 3 across all projects? Please answer as a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-14"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-04"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-10"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 140, "goal": "Could you help me update the due dates of all tasks in the \"Household Chores\" project to be one week later? Afterward, please tell me the total number of tasks with priority 4 across all projects. Please answer as a number. ", "subgoals": [3], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-27"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-29"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-07-01"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-07-01"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 141, "goal": "Please assist me in marking all tasks in the \"Picnic Preparation\" project with priority 3 or higher as completed. Then, could you tell me the total number of completed tasks across all projects? Please answer as a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}]}, "init_config": {"current_date": "2015-06-23", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 142, "goal": "I need help in updating the due dates of all tasks in the \"Homework and Assignments\" project to be one day earlier. After that, could you provide me with the total number of tasks across all projects? Please answer as a number. ", "subgoals": [14], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-05-31"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-11"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-05"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 143, "goal": "Please help me mark all tasks in the \"Extracurricular Activities\" project with a due date before \"2015-06-22\" as completed. Then, could you tell me the total number of completed tasks across all projects? Please answer as a number. ", "subgoals": [3], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-21", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 144, "goal": "Please help me mark all tasks in the \"Picnic Preparation\" project with priority 2 or higher as completed. Then, could you tell me the total number of completed tasks across all projects? Please answer as a number. ", "subgoals": [2], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}]}, "init_config": {"current_date": "2015-06-23", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 145, "goal": "Could you assist me in updating the due dates of all tasks in the \"Science Fair Project\" project to be one day earlier? Afterward, please tell me the total number of tasks with priority 3 across all projects. Please answer as a number. ", "subgoals": [4], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-19"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-20"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-22"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 146, "goal": "Please help me update the due dates of all tasks in the \"Household Chores\" project to be one day later. After that, could you tell me the total number of tasks with priority 4 across all projects? Please answer as a number. ", "subgoals": [2], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-21"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-23"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-25"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-25"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-05", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 147, "goal": "Could you assist me in marking all tasks in the \"Picnic Preparation\" project with a due date before \"2015-06-25\" as completed? Then, could you tell me the total number of completed tasks across all projects? Please answer as a number. ", "subgoals": [1], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-24", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 148, "goal": "Can you mark all tasks in the \"Extracurricular Activities\" project with a priority of 4 as completed? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-12", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 149, "goal": "Please help me find and delete all tasks in the \"Household Chores\" project that have a duration less than 30 minutes. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-15", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 150, "goal": "Can you update the due date of tasks in the \"Picnic Preparation\" project to be three days from today, except those with a priority of 3? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-25"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-22", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 151, "goal": "Help me mark all tasks in the \"Science Fair Project\" project with a duration longer than 90 minutes as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-18", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 152, "goal": "Can you update the due date of tasks in the \"Household Chores\" project to be tomorrow, except those with a priority of 2? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2023-10-05"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2023-10-05"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2023-10-05"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-20", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 153, "goal": "Please help me mark all tasks in the \"Homework and Assignments\" project with a duration less than 45 minutes as completed. ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-21"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-10", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
{"id": 154, "goal": "Can you update the due date of tasks in the \"Science Fair Project\" project to be four days from today, except those with a priority of 4? ", "subgoals": ["done"], "additional_info": {"answer": {"projects": [{"order": 1, "color": "charcoal", "name": "Homework and Assignments", "is_favorite": false}, {"order": 2, "color": "charcoal", "name": "Extracurricular Activities", "is_favorite": false}, {"order": 3, "color": "charcoal", "name": "Science Fair Project", "is_favorite": false}, {"order": 4, "color": "charcoal", "name": "Household Chores", "is_favorite": false}, {"order": 5, "color": "charcoal", "name": "Picnic Preparation", "is_favorite": false}], "tasks": [{"order": 1, "content": "Solve algebra equations", "is_completed": false, "priority": 1, "due_date": "2015-06-01"}, {"order": 2, "content": "Conduct a chemistry experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-12"}, {"order": 3, "content": "Read and summarize a history chapter", "is_completed": false, "priority": 3, "due_date": "2015-06-06"}, {"order": 1, "content": "Attend soccer practice", "is_completed": false, "priority": 4, "due_date": "2015-06-12"}, {"order": 2, "content": "Rehearse with the school band", "is_completed": false, "priority": 4, "due_date": "2015-06-02"}, {"order": 3, "content": "Attend debate club meeting", "is_completed": false, "priority": 1, "due_date": "2015-06-08"}, {"order": 1, "content": "Design and conduct a biology experiment", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Prepare presentation for science fair", "is_completed": false, "priority": 3, "due_date": "2015-06-23"}, {"order": 3, "content": "Write a research paper on the experiment", "is_completed": false, "priority": 1, "due_date": "2015-06-23"}, {"order": 1, "content": "Clean the kitchen", "is_completed": false, "priority": 2, "due_date": "2015-06-20"}, {"order": 2, "content": "Do the laundry", "is_completed": false, "priority": 3, "due_date": "2015-06-22"}, {"order": 3, "content": "Tidy up the living room", "is_completed": false, "priority": 1, "due_date": "2015-06-24"}, {"order": 4, "content": "Water the plants", "is_completed": false, "priority": 4, "due_date": "2015-06-24"}, {"order": 1, "content": "Purchase picnic supplies", "is_completed": false, "priority": 2, "due_date": "2015-06-23"}, {"order": 2, "content": "Select picnic location and plan activities", "is_completed": false, "priority": 3, "due_date": "2015-06-25"}]}, "init_config": {"current_date": "2015-06-19", "current_location": "New York"}, "goal_type": 0, "tool": "todo"}, "difficulty": "hard"}
