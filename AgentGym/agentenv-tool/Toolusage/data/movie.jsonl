{"id": 0, "goal": "Where was the director of Minions: The Rise of Gru born? Please answer me with the birthplace as a string.", "subgoals": ["Tucson, Arizona, USA"], "additional_info": {"answer": "Tucson, Arizona, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 1, "goal": "Is the director of The Dark Knight the same as the director of Spider-Man: Across the Spider-Verse? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 2, "goal": "Avatar versus Forrest Gump, which has a higher rating? Please answer me with the film name as a string.", "subgoals": ["Forrest Gump"], "additional_info": {"answer": "Forrest Gump", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 3, "goal": "Did the movie 'How do you know' make a profit? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 4, "goal": "In 'The Dark Knight' and 'The Pursuit of Happyness', which of the two movies had higher earnings? Please answer me with the film name as a string.", "subgoals": ["'The Dark Knight'"], "additional_info": {"answer": "'The Dark Knight'", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 5, "goal": "Does Top Gun: Maverick and Black Panther: Wakanda Forever share a common genre? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 6, "goal": "What character did Cate Blanchett play in Don't Look Up? Please answer me with the character's name as a string.", "subgoals": ["Brie Evantee"], "additional_info": {"answer": "Brie Evantee", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 7, "goal": "The actress who played Evelyn Wang in \"Everything Everywhere All at Once\", what role did she play in \"Crouching Tiger, Hidden Dragon\"?", "subgoals": ["Yu Shu Lien"], "additional_info": {"answer": "Yu Shu Lien", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 8, "goal": "Which movie was released earlier, Turning Red or Everything Everywhere All at Once?", "subgoals": ["Turning Red"], "additional_info": {"answer": "Turning Red", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 9, "goal": "Is Black Panther: Wakanda Forever a movie produced by Marvel Studios? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 10, "goal": "Is the movie 'Disenchanted' a Japanese film? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 11, "goal": "Are 'Disenchanted' and 'Lighting Up the Stars' movies from the same country? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 12, "goal": "May I ask, in which movie did Brad Pitt first gain attention?", "subgoals": ["Thelma & Louise"], "additional_info": {"answer": "Thelma & Louise", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 13, "goal": "Who is the director of the film that Brad Pitt first gained attention? Please answer me with the director's name as a string.", "subgoals": ["Robert Redford"], "additional_info": {"answer": "Robert Redford", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 14, "goal": "In the movie for which Michelle Yeoh won the Oscar for Best Actress, what role did Jamie Lee Curtis play? Please answer me with the character's name as a string.", "subgoals": ["Deirdre Beaubeirdre"], "additional_info": {"answer": "Deirdre Beaubeirdre", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 15, "goal": "I am very interested in the actress who plays Wanda Maximoff in Doctor Strange in the Multiverse of Madness. What is her IMDB ID? Please provide the IMDB ID as a string.", "subgoals": ["nm0647634"], "additional_info": {"answer": "nm0647634", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 16, "goal": "Brendan Fraser won the Academy Award for Best Actor in 2023. I would like to see the movie for which he won the award. What is its official Korean title? Please provide the title as a string.", "subgoals": ["\ub354 \uc6e8\uc77c"], "additional_info": {"answer": "\ub354 \uc6e8\uc77c", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 17, "goal": "Jessica Chastain won the Academy Award for Best Actress in 2022. Please give an overview in Dutch about the movie for which she won the award.", "subgoals": ["In de jaren 70 en 80 cre\u00eberden Tammy Faye en haar man, Jim Bakker, vanuit het niets 's werelds grootste religieuze omroep en themapark en werden ze vereerd vanwege hun boodschap van liefde, acceptatie en welvaart. Het duurde echter niet lang voordat financi\u00eble onregelmatigheden, sluwe rivalen en schandalen hun zorgvuldig opgebouwde rijk ten val brachten."], "additional_info": {"answer": "In de jaren 70 en 80 cre\u00eberden Tammy Faye en haar man, Jim Bakker, vanuit het niets 's werelds grootste religieuze omroep en themapark en werden ze vereerd vanwege hun boodschap van liefde, acceptatie en welvaart. Het duurde echter niet lang voordat financi\u00eble onregelmatigheden, sluwe rivalen en schandalen hun zorgvuldig opgebouwde rijk ten val brachten.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 18, "goal": "What is the budget of the movie \"Inception\"? Please provide the budget as a number.", "subgoals": [160000000], "additional_info": {"answer": 160000000, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 19, "goal": "Where was the actress Emma Stone born? Please answer me with the location as a string.", "subgoals": ["Scottsdale, Arizona, USA"], "additional_info": {"answer": "Scottsdale, Arizona, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 20, "goal": "Is the movie \"The Matrix Resurrections\" directed by Lana Wachowski? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 21, "goal": "Which movie has a higher revenue, \"Avengers: Endgame\" or \"Jurassic World\"? Please answer me with the film name as a string.", "subgoals": ["Avengers: Endgame"], "additional_info": {"answer": "Avengers: Endgame", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 22, "goal": "Did the movie \"La La Land\" win any Oscars? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 23, "goal": "Do the movies \"The Godfather\" and \"Pulp Fiction\" share a common genre? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 24, "goal": "What is the release date of the movie \"Interstellar\"? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["2014-11-05"], "additional_info": {"answer": "2014-11-05", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 25, "goal": "Is the movie \"The Social Network\" based on a true story? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 26, "goal": "What is the biography of actor Leonardo DiCaprio?", "subgoals": ["Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world."], "additional_info": {"answer": "Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 27, "goal": "Is the movie \"The Revenant\" set in a post-apocalyptic world? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 28, "goal": "What is the average vote score of the movie \"The Godfather Part II\"? Please provide the answer as a number.", "subgoals": [8.576], "additional_info": {"answer": 8.576, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 29, "goal": "Who played the character of Jack Dawson in the movie \"Titanic\"? Please answer me with the actor's name as a string.", "subgoals": ["Leonardo DiCaprio"], "additional_info": {"answer": "Leonardo DiCaprio", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 30, "goal": "Is the movie \"The Silence of the Lambs\" a horror film? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 31, "goal": "What are the production companies of the movie \"Joker\"? Please provide the names as an array.", "subgoals": [["Bron Studios", "DC Films", "Joint Effort", "Village Roadshow Pictures", "Warner Bros. Pictures"]], "additional_info": {"answer": ["Bron Studios", "DC Films", "Joint Effort", "Village Roadshow Pictures", "Warner Bros. Pictures"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 32, "goal": "Is the movie \"The Grand Budapest Hotel\" directed by Wes Anderson? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 33, "goal": "What is the place of birth of actor Tom Hanks? Please answer me with the location as a string.", "subgoals": ["Concord, California, USA"], "additional_info": {"answer": "Concord, California, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 34, "goal": "Who directed the movie \"The Shawshank Redemption\"? Please answer me with the director's name as a string.", "subgoals": ["Frank Darabont"], "additional_info": {"answer": "Frank Darabont", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 35, "goal": "What is the release date of \"The Matrix Resurrections\"? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["2021-12-16"], "additional_info": {"answer": "2021-12-16", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 36, "goal": "Which production company worked on \"Jurassic Park\"? Please provide the names of the production companies in an array.", "subgoals": [["Amblin Entertainment", "Universal Pictures"]], "additional_info": {"answer": ["Amblin Entertainment", "Universal Pictures"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 37, "goal": "Who directed the movie \"Inception\"? Please answer me with the director's name as a string.", "subgoals": ["Christopher Nolan"], "additional_info": {"answer": "Christopher Nolan", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 38, "goal": "Does the movie \"Jurassic Park\" have any alternative titles? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 39, "goal": "What is the budget of \"The Avengers\"? Please provide the budget as a number.", "subgoals": [*********], "additional_info": {"answer": *********, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 40, "goal": "What is the release date of \"Interstellar\"? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["2014-11-05"], "additional_info": {"answer": "2014-11-05", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 41, "goal": "Is the actor Tom Hanks part of the cast in \"Saving Private Ryan\"? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 42, "goal": "Where was the actress Scarlett Johansson born? Please answer me with the location as a string.", "subgoals": ["New York City, New York, USA"], "additional_info": {"answer": "New York City, New York, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 43, "goal": "What genre does the movie \"The Shawshank Redemption\" belong to? Please answer me with a list of genres.", "subgoals": [["Crime", "Drama"]], "additional_info": {"answer": ["Crime", "Drama"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 44, "goal": "Did the movie \"The Lion King\" generate revenue? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 45, "goal": "Who wrote the screenplay for \"The Godfather\"? Please answer me with the name of the writer as a string.", "subgoals": ["Mario Puzo"], "additional_info": {"answer": "Mario Puzo", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 46, "goal": "Is there any translation available for the movie \"Pulp Fiction\"? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 47, "goal": "What is the IMDB id of actress Emma Stone? Please provide the answer as a string.", "subgoals": ["nm1297015"], "additional_info": {"answer": "nm1297015", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 48, "goal": "What is the Facebook id of actor Chris Evans? Please provide the answer as a string.", "subgoals": ["chrisevans"], "additional_info": {"answer": "chrisevans", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 49, "goal": "What is the Instagram id of actor Dwayne Johnson? Please provide the Instagram id as a string.", "subgoals": ["therock"], "additional_info": {"answer": "therock", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 50, "goal": "What is the Twitter id of actress Jennifer Lawrence? Please provide the Twitter id as a string.", "subgoals": ["JLawrence_RepUs"], "additional_info": {"answer": "JLawrence_RepUs", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 51, "goal": "Are there any keywords associated with the movie \"The Matrix\"? Please provide me with a list of keywords.", "subgoals": [["action hero", "artificial intelligence (a.i.)", "complex", "cyberpunk", "dream", "dream world", "dystopia", "fight", "gnosticism", "hacker", "insurgence", "man vs machine", "martial arts", "messiah", "philosophy", "prophecy", "saving the world", "self sacrifice", "simulated reality ", "truth", "virtual reality", "woman director"]], "additional_info": {"answer": ["action hero", "artificial intelligence (a.i.)", "complex", "cyberpunk", "dream", "dream world", "dystopia", "fight", "gnosticism", "hacker", "insurgence", "man vs machine", "martial arts", "messiah", "philosophy", "prophecy", "saving the world", "self sacrifice", "simulated reality ", "truth", "virtual reality", "woman director"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 52, "goal": "Who was the producer of the movie \"The Dark Knight Rises\"? Please provide the name of the producer as an array.", "subgoals": [["Charles Roven", "Christopher Nolan", "Emma Thomas"]], "additional_info": {"answer": ["Charles Roven", "Christopher Nolan", "Emma Thomas"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 53, "goal": "What is the place of birth of actor Robert Downey Jr.? Please answer me with the location as a string.", "subgoals": ["New York City, New York, USA"], "additional_info": {"answer": "New York City, New York, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 54, "goal": "Is the movie \"Inglourious Basterds\" produced by Quentin Tarantino? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 55, "goal": "Is the budget of The Godfather higher than the budget of Titanic? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 56, "goal": "Who directed the movie Inception? Please answer me with the director's name as a string.", "subgoals": ["Christopher Nolan"], "additional_info": {"answer": "Christopher Nolan", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 57, "goal": "What is the release date of The Shawshank Redemption? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1994-09-23"], "additional_info": {"answer": "1994-09-23", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 58, "goal": "Did the movie Jurassic Park earn more revenue than The Lion King? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 59, "goal": "Are there any common genres between The Matrix and Inception? Please answer me with a Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 60, "goal": "What role did Tom Hanks play in Saving Private Ryan? Please answer me with the character's name as a string.", "subgoals": ["Captain John H. Miller"], "additional_info": {"answer": "Captain John H. Miller", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 61, "goal": "Which movie was released first, Pulp Fiction or Forrest Gump? Please answer me with the film name as a string.", "subgoals": ["Forrest Gump"], "additional_info": {"answer": "Forrest Gump", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 62, "goal": "Is there any common production company between The Avengers and Iron Man? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 63, "goal": "Who composed the music for The Lord of the Rings: The Fellowship of the Ring? Please answer me with the composer's name as a string.", "subgoals": ["Howard Shore"], "additional_info": {"answer": "Howard Shore", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 64, "goal": "What is the place of birth of Scarlett Johansson? Please answer me with the location as a string.", "subgoals": ["New York City, New York, USA"], "additional_info": {"answer": "New York City, New York, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 65, "goal": "Did the movie Interstellar make a profit? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 66, "goal": "Are there any common production countries between Gladiator and Braveheart? Please answer me with the name of the country as a string.", "subgoals": ["United States of America"], "additional_info": {"answer": "United States of America", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 67, "goal": "Who wrote the screenplay for The Social Network? Please answer me with the name of the writer as a string.", "subgoals": ["Aaron Sorkin"], "additional_info": {"answer": "Aaron Sorkin", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 68, "goal": "What is the biography of Leonardo DiCaprio?", "subgoals": ["Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world."], "additional_info": {"answer": "Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 69, "goal": "What role did Morgan Freeman play in The Dark Knight? Please answer me with the character name as a string.", "subgoals": ["Lucius Fox"], "additional_info": {"answer": "Lucius Fox", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 70, "goal": "Are there any common keywords between Fight Club and The Matrix? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 71, "goal": "What is the birthday of Brad Pitt? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["1963-12-18"], "additional_info": {"answer": "1963-12-18", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 72, "goal": "Which movie has a higher vote average, The Godfather or The Dark Knight? Please answer me with the film name as a string.", "subgoals": ["The Godfather"], "additional_info": {"answer": "The Godfather", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 73, "goal": "Who directed the movie Joker? Please answer me with the director's name as a string.", "subgoals": ["Todd Phillips"], "additional_info": {"answer": "Todd Phillips", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 74, "goal": "What is the place of birth of Christopher Nolan? Please answer me with the location as a string.", "subgoals": ["Westminster, London, England, UK"], "additional_info": {"answer": "Westminster, London, England, UK", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 75, "goal": "Are there any common production companies between The Silence of the Lambs and Schindler's List? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 76, "goal": "Who composed the music for The Lion King? Please answer me with the composer's name as a string.", "subgoals": ["Hans Zimmer"], "additional_info": {"answer": "Hans Zimmer", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 77, "goal": "What is the biography of Steven Spielberg?", "subgoals": ["Steven Allan Spielberg (born December 18, 1946) is an American film director, writer and producer. A major figure of the New Hollywood era and pioneer of the modern blockbuster, he is the most commercially successful director of all time. Spielberg is the recipient of various accolades, including three Academy Awards, a Kennedy Center honor, four Directors Guild of America Awards, two BAFTA Awards, a Cecil B. DeMille Award and an AFI Life Achievement Award. Seven of his films have been inducted into the National Film Registry by the Library of Congress as \"culturally, historically or aesthetically significant\".\n\nSpielberg was born in Cincinnati, Ohio, and grew up in Phoenix, Arizona. He moved to California and studied film in college. After directing several episodes for television including Night Gallery and Columbo, he directed the television film Duel (1971) which gained acclaim from critics and audiences. He made his directorial film debut with The Sugarland Express (1974), and became a household name with the 1975 summer blockbuster Jaws. He then directed huge box office successes Close Encounters of the Third Kind (1977), E.T. the Extra-Terrestrial (1982) and the Indiana Jones original trilogy (1981-89). Spielberg subsequently explored drama in the acclaimed The Color Purple (1985) and Empire of the Sun (1987).\n\nAfter a brief hiatus, Spielberg directed the science fiction thriller Jurassic Park (1993), the highest-grossing film ever at the time, and the Holocaust drama Schindler's List (1993), which has often been listed as one of the greatest films ever made. He won the Academy Award for Best Director for the latter and for the 1998 World War II epic Saving Private Ryan. Spielberg continued in the 2000s with science fiction films A.I. Artificial Intelligence (2001), Minority Report (2002) and War of the Worlds (2005). He also directed the adventure films The Adventures of Tintin (2011) and Ready Player One (2018); the historical dramas Amistad (1997), Munich (2005), War Horse (2011), Lincoln (2012), Bridge of Spies (2015) and The Post (2017); the musical West Side Story (2021); and the semi-autobiographical drama The Fabelmans (2022). He has been a producer on several successful films, including Poltergeist (1982), Gremlins (1984), Back to the Future (1985) and Who Framed Roger Rabbit (1988) as well as the miniseries Band of Brothers (2001).\n\nSpielberg co-founded Amblin Entertainment and DreamWorks, and has served as a producer for many successful films and television series. He is also known for his long collaboration with the composer John Williams, with whom he has worked for all but five of his feature films. Several of Spielberg's works are among the highest-grossing and greatest films all time. Premiere ranked him first place in the list of 100 Most Powerful People in Movies in 2003. In 2013, Time listed him as one of the 100 most influential people."], "additional_info": {"answer": "Steven Allan Spielberg (born December 18, 1946) is an American film director, writer and producer. A major figure of the New Hollywood era and pioneer of the modern blockbuster, he is the most commercially successful director of all time. Spielberg is the recipient of various accolades, including three Academy Awards, a Kennedy Center honor, four Directors Guild of America Awards, two BAFTA Awards, a Cecil B. DeMille Award and an AFI Life Achievement Award. Seven of his films have been inducted into the National Film Registry by the Library of Congress as \"culturally, historically or aesthetically significant\".\n\nSpielberg was born in Cincinnati, Ohio, and grew up in Phoenix, Arizona. He moved to California and studied film in college. After directing several episodes for television including Night Gallery and Columbo, he directed the television film Duel (1971) which gained acclaim from critics and audiences. He made his directorial film debut with The Sugarland Express (1974), and became a household name with the 1975 summer blockbuster Jaws. He then directed huge box office successes Close Encounters of the Third Kind (1977), E.T. the Extra-Terrestrial (1982) and the Indiana Jones original trilogy (1981-89). Spielberg subsequently explored drama in the acclaimed The Color Purple (1985) and Empire of the Sun (1987).\n\nAfter a brief hiatus, Spielberg directed the science fiction thriller Jurassic Park (1993), the highest-grossing film ever at the time, and the Holocaust drama Schindler's List (1993), which has often been listed as one of the greatest films ever made. He won the Academy Award for Best Director for the latter and for the 1998 World War II epic Saving Private Ryan. Spielberg continued in the 2000s with science fiction films A.I. Artificial Intelligence (2001), Minority Report (2002) and War of the Worlds (2005). He also directed the adventure films The Adventures of Tintin (2011) and Ready Player One (2018); the historical dramas Amistad (1997), Munich (2005), War Horse (2011), Lincoln (2012), Bridge of Spies (2015) and The Post (2017); the musical West Side Story (2021); and the semi-autobiographical drama The Fabelmans (2022). He has been a producer on several successful films, including Poltergeist (1982), Gremlins (1984), Back to the Future (1985) and Who Framed Roger Rabbit (1988) as well as the miniseries Band of Brothers (2001).\n\nSpielberg co-founded Amblin Entertainment and DreamWorks, and has served as a producer for many successful films and television series. He is also known for his long collaboration with the composer John Williams, with whom he has worked for all but five of his feature films. Several of Spielberg's works are among the highest-grossing and greatest films all time. Premiere ranked him first place in the list of 100 Most Powerful People in Movies in 2003. In 2013, Time listed him as one of the 100 most influential people.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 78, "goal": "Did the movie The Matrix Revolutions make a profit? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 79, "goal": "Are there any common production countries between Avatar and Titanic? Please answer me with the name of the country as a string.", "subgoals": ["United States of America"], "additional_info": {"answer": "United States of America", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 80, "goal": "Who wrote the screenplay for The Shawshank Redemption? Please answer me with the name of the writer as a string.", "subgoals": ["Frank Darabont"], "additional_info": {"answer": "Frank Darabont", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 81, "goal": "What is the birthday of Emma Watson? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1990-04-15"], "additional_info": {"answer": "1990-04-15", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 82, "goal": "Did the movie The Lord of the Rings: The Return of the King win any Academy Awards? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 83, "goal": "Are there any common keywords between Forrest Gump and Titanic? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 84, "goal": "What role did Robert De Niro play in Taxi Driver? Please answer me with the character name as a string.", "subgoals": ["Travis Bickle"], "additional_info": {"answer": "Travis Bickle", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 85, "goal": "Which movie was released first, The Terminator or Die Hard? Please answer me with the film name as a string.", "subgoals": ["The Terminator"], "additional_info": {"answer": "The Terminator", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 86, "goal": "Is there any common production company between The Dark Knight Rises and Inception? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 87, "goal": "Who directed the movie Schindler's List? Please answer me with the director's name as a string.", "subgoals": ["Steven Spielberg"], "additional_info": {"answer": "Steven Spielberg", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 88, "goal": "What is the place of birth of Quentin Tarantino? Please answer me with the birthplace as a string.", "subgoals": ["Knoxville, Tennessee, USA"], "additional_info": {"answer": "Knoxville, Tennessee, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 89, "goal": "Did the movie The Green Mile earn more revenue than The Shawshank Redemption? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 90, "goal": "Are there any common production countries between The Godfather Part II and Goodfellas? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 91, "goal": "Who wrote the screenplay for Inglourious Basterds? Please answer me with the name of the writer as a string.", "subgoals": ["Quentin Tarantino"], "additional_info": {"answer": "Quentin Tarantino", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 92, "goal": "What is the biography of Martin Scorsese? Please provide me with a brief summary.", "subgoals": ["Martin Charles Scorsese (born November 17, 1942) is an American film director, producer, screenwriter, and actor. One of the major figures of the New Hollywood era, he is widely regarded as one of the greatest and most influential directors in film history. Scorsese's body of work explores themes such as Italian-American identity, Catholic concepts of guilt and redemption, faith, machismo, nihilism, crime and sectarianism. Many of his films are known for their depiction of violence and the liberal use of profanity. Scorsese has also dedicated his life to film preservation and film restoration by founding the nonprofit organization The Film Foundation in 1990, as well as the World Cinema Foundation in 2007 and the African Film Heritage Project in 2017.\n\nScorsese studied at New York University (NYU), where he received a bachelor's degree in English literature in 1964, and received a master's degree in fine arts in film from NYU's Tisch School of the Arts in 1968. In 1967 Scorsese's first feature film Who's That Knocking at My Door was released and was accepted into the Chicago Film Festival, where critic Roger Ebert saw it and called it \"a marvelous evocation of American city life, announcing the arrival of an important new director\".\n\nHe has established a filmmaking history involving repeat collaborations with actors and film technicians, including nine films made with Robert De Niro. His films with De Niro are the psychological thriller Taxi Driver (1976), the biographical sports drama Raging Bull (1980), the satirical black comedy The King of Comedy (1982), the musical drama New York, New York (1977), the psychological thriller Cape Fear (1991), and the crime films Mean Streets (1973), Goodfellas (1990), Casino (1995) and The Irishman (2019). Scorsese has also been noted for his collaborations with actor Leonardo DiCaprio, having directed him in five films: the historical epic Gangs of New York (2002), the Howard Hughes biography The Aviator (2004), the crime thriller The Departed (2006), the psychological thriller Shutter Island (2010), and the Wall Street black comedy The Wolf of Wall Street (2013). The Departed won Scorsese an Academy Award for Best Director, and for Best Picture. Scorsese is also known for his long-time collaboration with film editor Thelma Schoonmaker, who has edited every Scorsese film beginning with Raging Bull. Scorsese's other film work includes the black comedy After Hours (1985), the romantic drama The Age of Innocence (1993), the children's adventure drama Hugo (2011), and the religious epics The Last Temptation of Christ (1988), Kundun (1997) and Silence (2016)."], "additional_info": {"answer": "Martin Charles Scorsese (born November 17, 1942) is an American film director, producer, screenwriter, and actor. One of the major figures of the New Hollywood era, he is widely regarded as one of the greatest and most influential directors in film history. Scorsese's body of work explores themes such as Italian-American identity, Catholic concepts of guilt and redemption, faith, machismo, nihilism, crime and sectarianism. Many of his films are known for their depiction of violence and the liberal use of profanity. Scorsese has also dedicated his life to film preservation and film restoration by founding the nonprofit organization The Film Foundation in 1990, as well as the World Cinema Foundation in 2007 and the African Film Heritage Project in 2017.\n\nScorsese studied at New York University (NYU), where he received a bachelor's degree in English literature in 1964, and received a master's degree in fine arts in film from NYU's Tisch School of the Arts in 1968. In 1967 Scorsese's first feature film Who's That Knocking at My Door was released and was accepted into the Chicago Film Festival, where critic Roger Ebert saw it and called it \"a marvelous evocation of American city life, announcing the arrival of an important new director\".\n\nHe has established a filmmaking history involving repeat collaborations with actors and film technicians, including nine films made with Robert De Niro. His films with De Niro are the psychological thriller Taxi Driver (1976), the biographical sports drama Raging Bull (1980), the satirical black comedy The King of Comedy (1982), the musical drama New York, New York (1977), the psychological thriller Cape Fear (1991), and the crime films Mean Streets (1973), Goodfellas (1990), Casino (1995) and The Irishman (2019). Scorsese has also been noted for his collaborations with actor Leonardo DiCaprio, having directed him in five films: the historical epic Gangs of New York (2002), the Howard Hughes biography The Aviator (2004), the crime thriller The Departed (2006), the psychological thriller Shutter Island (2010), and the Wall Street black comedy The Wolf of Wall Street (2013). The Departed won Scorsese an Academy Award for Best Director, and for Best Picture. Scorsese is also known for his long-time collaboration with film editor Thelma Schoonmaker, who has edited every Scorsese film beginning with Raging Bull. Scorsese's other film work includes the black comedy After Hours (1985), the romantic drama The Age of Innocence (1993), the children's adventure drama Hugo (2011), and the religious epics The Last Temptation of Christ (1988), Kundun (1997) and Silence (2016).", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 93, "goal": "Did the movie The Revenant win any Academy Awards? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 94, "goal": "Is the genre of the movie \"Inception\" the same as the genre of \"Interstellar\"? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 95, "goal": "Who directed the movie \"Pulp Fiction\"? Please answer me with the director's name as a string.", "subgoals": ["Quentin Tarantino"], "additional_info": {"answer": "Quentin Tarantino", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 96, "goal": "What is the release date of the movie \"The Shawshank Redemption\"? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1994-09-23"], "additional_info": {"answer": "1994-09-23", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 97, "goal": "Does the movie \"Jurassic Park\" have the same production company as \"The Matrix\"? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 98, "goal": "What is the budget of the movie \"Titanic\"? Please provide the budget as a number.", "subgoals": [*********], "additional_info": {"answer": *********, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 99, "goal": "Is the actress who played Hermione Granger in the Harry Potter series the same as the actress who played Belle in \"Beauty and the Beast\" (2017)? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 100, "goal": "What is the biography of the actor who played Tony Stark in the Marvel Cinematic Universe? Please answer me with a string.", "subgoals": ["Robert John Downey Jr. (born April 4, 1966) is an American actor and producer. His career has been characterized by critical and popular success in his youth, followed by a period of substance abuse and legal troubles, before a resurgence of commercial success later in his career. In 2008, Downey was named by Time magazine among the 100 most influential people in the world, and from 2013 to 2015, he was listed by Forbes as Hollywood's highest-paid actor.\n\nAt the age of five, he made his acting debut in his father Robert Downey Sr.'s film Pound in 1970. He subsequently worked with the Brat Pack in the teen films Weird Science (1985) and Less than Zero (1987). In 1992, Downey portrayed the title character in the biopic Chaplin, for which he was nominated for the Academy Award for Best Actor and won a BAFTA Award. Following a stint at the Corcoran Substance Abuse Treatment Facility on drug charges, he joined the TV series Ally McBeal, for which he won a Golden Globe Award. He was fired from the show in the wake of drug charges in 2000 and 2001. He stayed in a court-ordered drug treatment program and has maintained his sobriety since 2003.\n\nInitially, completion bond companies would not insure Downey, until Mel Gibson paid the insurance bond for the 2003 film The Singing Detective. He went on to star in the black comedy Kiss Kiss Bang Bang (2005), the thriller Zodiac (2007), and the action comedy Tropic Thunder (2008); for the latter, he was nominated for an Academy Award for Best Supporting Actor.\n\nDowney gained global recognition for starring as Tony Stark / Iron Man in ten films within the Marvel Cinematic Universe, beginning with Iron Man (2008), and leading up to Avengers: Endgame (2019). He has also played the title character in Guy Ritchie's Sherlock Holmes (2009), which earned him his second Golden Globe, and its sequel, Sherlock Holmes: A Game of Shadows (2011).\n\nIn 2024 he won his first Academy Award for Best Supporting Actor thanks to his work in \"Oppenheimer.\""], "additional_info": {"answer": "Robert John Downey Jr. (born April 4, 1966) is an American actor and producer. His career has been characterized by critical and popular success in his youth, followed by a period of substance abuse and legal troubles, before a resurgence of commercial success later in his career. In 2008, Downey was named by Time magazine among the 100 most influential people in the world, and from 2013 to 2015, he was listed by Forbes as Hollywood's highest-paid actor.\n\nAt the age of five, he made his acting debut in his father Robert Downey Sr.'s film Pound in 1970. He subsequently worked with the Brat Pack in the teen films Weird Science (1985) and Less than Zero (1987). In 1992, Downey portrayed the title character in the biopic Chaplin, for which he was nominated for the Academy Award for Best Actor and won a BAFTA Award. Following a stint at the Corcoran Substance Abuse Treatment Facility on drug charges, he joined the TV series Ally McBeal, for which he won a Golden Globe Award. He was fired from the show in the wake of drug charges in 2000 and 2001. He stayed in a court-ordered drug treatment program and has maintained his sobriety since 2003.\n\nInitially, completion bond companies would not insure Downey, until Mel Gibson paid the insurance bond for the 2003 film The Singing Detective. He went on to star in the black comedy Kiss Kiss Bang Bang (2005), the thriller Zodiac (2007), and the action comedy Tropic Thunder (2008); for the latter, he was nominated for an Academy Award for Best Supporting Actor.\n\nDowney gained global recognition for starring as Tony Stark / Iron Man in ten films within the Marvel Cinematic Universe, beginning with Iron Man (2008), and leading up to Avengers: Endgame (2019). He has also played the title character in Guy Ritchie's Sherlock Holmes (2009), which earned him his second Golden Globe, and its sequel, Sherlock Holmes: A Game of Shadows (2011).\n\nIn 2024 he won his first Academy Award for Best Supporting Actor thanks to his work in \"Oppenheimer.\"", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 101, "goal": "Did the actor who played Batman in \"The Dark Knight\" also appear in \"American Psycho\"? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 102, "goal": "What are the alternative titles for the movie \"La La Land\"? Please provide the alternative titles as an array.", "subgoals": [["A\u015f\u0131klar \u015eehri", "La La Land: Kalifornijas sap\u0146i", "La ciudad de las estrellas", "LaLaLand", "\u0633\u0631\u0632\u0645\u06cc\u0646 \u0631\u0648\u06cc\u0627\u0647\u0627", "\u0644\u0627 \u0644\u0627 \u0644\u0646\u062f", "\u0644\u0627\u0644\u0627 \u0644\u0646\u062f", "\u0e19\u0e04\u0e23\u0e14\u0e32\u0e23\u0e32", "\u30e9\u30fb\u30e9\u30fb\u30e9\u30f3\u30c9\uff1a2016", "\ub77c\ub77c\ub79c\ub4dc"]], "additional_info": {"answer": ["A\u015f\u0131klar \u015eehri", "La La Land: Kalifornijas sap\u0146i", "La ciudad de las estrellas", "LaLaLand", "\u0633\u0631\u0632\u0645\u06cc\u0646 \u0631\u0648\u06cc\u0627\u0647\u0627", "\u0644\u0627 \u0644\u0627 \u0644\u0646\u062f", "\u0644\u0627\u0644\u0627 \u0644\u0646\u062f", "\u0e19\u0e04\u0e23\u0e14\u0e32\u0e23\u0e32", "\u30e9\u30fb\u30e9\u30fb\u30e9\u30f3\u30c9\uff1a2016", "\ub77c\ub77c\ub79c\ub4dc"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 103, "goal": "Who wrote the screenplay for the movie \"The Social Network\"? Please answer me with the name of the writer as a string.", "subgoals": ["Aaron Sorkin"], "additional_info": {"answer": "Aaron Sorkin", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 104, "goal": "Is the movie \"The Godfather\" set in the same country as \"The Lord of the Rings: The Fellowship of the Ring\"? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 105, "goal": "What are the keywords associated with the movie \"Fight Club\"? Please give me an array of keywords.", "subgoals": [["alter ego", "based on novel or book", "breaking the fourth wall", "dissociative identity disorder", "dual identity", "dystopia", "fight", "insomnia", "nihilism", "quitting a job", "rage and hate", "self destructiveness", "split personality", "support group"]], "additional_info": {"answer": ["alter ego", "based on novel or book", "breaking the fourth wall", "dissociative identity disorder", "dual identity", "dystopia", "fight", "insomnia", "nihilism", "quitting a job", "rage and hate", "self destructiveness", "split personality", "support group"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 106, "goal": "What is the birthday of the actress who played Rey in the Star Wars sequel trilogy? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["1992-04-10"], "additional_info": {"answer": "1992-04-10", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 107, "goal": "Did the actress who played Black Widow in the Marvel Cinematic Universe also appear in \"Lost in Translation\"? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 108, "goal": "What is the revenue of the movie \"Avengers: Endgame\"? Please provide the revenue as a number.", "subgoals": [2800000000], "additional_info": {"answer": 2800000000, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 109, "goal": "Who composed the music for the movie \"The Lion King\" (1994)? Please answer me with the composer's name as a string.", "subgoals": ["Hans Zimmer"], "additional_info": {"answer": "Hans Zimmer", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 110, "goal": "What is the release date of the movie \"Back to the Future\"? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["1985-07-03"], "additional_info": {"answer": "1985-07-03", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 111, "goal": "Is the average vote score of the movie \"The Godfather\" higher than that of \"The Dark Knight\"? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 112, "goal": "What is the place of birth of the actor who played Jack Dawson in \"Titanic\"? Please provide the birthplace as a string.", "subgoals": ["Los Angeles, California, USA"], "additional_info": {"answer": "Los Angeles, California, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 113, "goal": "Are the production countries of \"The Matrix\" and \"Inception\" the same? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 114, "goal": "Can you provide the biography of the actress who portrayed Rey in the Star Wars sequel trilogy? Please answer me with a brief description.", "subgoals": ["Daisy Jazz Isobel Ridley (born 10 April 1992) is an English actress, who rose to prominence for her role as Rey in the Star Wars sequel trilogy: The Force Awakens (2015), The Last Jedi (2017), and The Rise of Skywalker (2019).\n\nShe also appeared in the mystery film Murder on the Orient Express (2017), played the title character of the romantic drama Ophelia (2018), and has done occasional voice acting, notably the live-action/animated film Peter Rabbit (2018) and video games such as 12 Minutes.\n\nDescription above from the Wikipedia article Daisy Ridley, licensed under CC-BY-SA, full list of contributors on Wikipedia."], "additional_info": {"answer": "Daisy Jazz Isobel Ridley (born 10 April 1992) is an English actress, who rose to prominence for her role as Rey in the Star Wars sequel trilogy: The Force Awakens (2015), The Last Jedi (2017), and The Rise of Skywalker (2019).\n\nShe also appeared in the mystery film Murder on the Orient Express (2017), played the title character of the romantic drama Ophelia (2018), and has done occasional voice acting, notably the live-action/animated film Peter Rabbit (2018) and video games such as 12 Minutes.\n\nDescription above from the Wikipedia article Daisy Ridley, licensed under CC-BY-SA, full list of contributors on Wikipedia.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 115, "goal": "What is the budget of the movie directed by Christopher Nolan that explores the concept of dreams within dreams? Please provide the budget as a number.", "subgoals": [160000000], "additional_info": {"answer": 160000000, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 116, "goal": "Which actor starred in both \"Inception\" and \"The Wolf of Wall Street\"? Please answer me with the actor's name as a string.", "subgoals": ["Leonardo DiCaprio"], "additional_info": {"answer": "Leonardo DiCaprio", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 117, "goal": "What are the production countries of the movie \"Avatar\"? Please answer me with an array of country names.", "subgoals": [["United Kingdom", "United States of America"]], "additional_info": {"answer": ["United Kingdom", "United States of America"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 118, "goal": "Can you provide the alternative titles for the movie \"Pulp Fiction\"? Give me an array of alternative titles.", "subgoals": [["Fiction pulpeuse", "Lubene", "Makulatura", "Pulp Fiction", "Pulp Fiction - Chronological Cut", "Pulp Fiction - Tarinoita v\u00e4kivallasta", "Pulp Fiction: Historky z podsvetia", "Sifrut Zolla", "Sund", "Tiempos Violentos", "parup fikusyon", "\u03bc\u03c5\u03b8\u03bf\u03c0\u03bb\u03b1\u03c3\u03af\u03b1 \u03c0\u03bf\u03bb\u03c4\u03bf\u03cd", "\u0415\u0432\u0442\u0438\u043d\u0438 \u043f\u0440\u0438\u043a\u0430\u0437\u043d\u0438", "\u041a\u0440\u0438\u043ci\u043d\u0430\u043b\u044c\u043d\u0435 \u0447\u0442\u0438\u0432\u043e", "\u041a\u0440\u0438\u043c\u0438\u043d\u0430\u043b\u044c\u043d\u043e\u0435 \u0447\u0442\u0438\u0432\u043e", "\u041f\u0435\u0442\u043f\u0430\u0440\u0430\u0447\u043a\u0435 \u043f\u0440\u0438\u0447\u0435", "\u092a\u0932\u094d\u092a \u092b\u093f\u0915\u094d\u0936\u0928", "\u4f4e\u4fd7\u5c0f\u8bf4", "\u5371\u96aa\u4eba\u7269", "\u9ed1\u8272\u8ffd\u7ddd\u4ee4", "\ud384\ud504 \ud53d\uc158"]], "additional_info": {"answer": ["Fiction pulpeuse", "Lubene", "Makulatura", "Pulp Fiction", "Pulp Fiction - Chronological Cut", "Pulp Fiction - Tarinoita v\u00e4kivallasta", "Pulp Fiction: Historky z podsvetia", "Sifrut Zolla", "Sund", "Tiempos Violentos", "parup fikusyon", "\u03bc\u03c5\u03b8\u03bf\u03c0\u03bb\u03b1\u03c3\u03af\u03b1 \u03c0\u03bf\u03bb\u03c4\u03bf\u03cd", "\u0415\u0432\u0442\u0438\u043d\u0438 \u043f\u0440\u0438\u043a\u0430\u0437\u043d\u0438", "\u041a\u0440\u0438\u043ci\u043d\u0430\u043b\u044c\u043d\u0435 \u0447\u0442\u0438\u0432\u043e", "\u041a\u0440\u0438\u043c\u0438\u043d\u0430\u043b\u044c\u043d\u043e\u0435 \u0447\u0442\u0438\u0432\u043e", "\u041f\u0435\u0442\u043f\u0430\u0440\u0430\u0447\u043a\u0435 \u043f\u0440\u0438\u0447\u0435", "\u092a\u0932\u094d\u092a \u092b\u093f\u0915\u094d\u0936\u0928", "\u4f4e\u4fd7\u5c0f\u8bf4", "\u5371\u96aa\u4eba\u7269", "\u9ed1\u8272\u8ffd\u7ddd\u4ee4", "\ud384\ud504 \ud53d\uc158"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 119, "goal": "Who wrote the screenplay for the movie adaptation of \"The Lord of the Rings: The Return of the King\"? Please provide the names as an array.", "subgoals": [["Peter Jackson", "Philippa Boyens"]], "additional_info": {"answer": ["Peter Jackson", "Philippa Boyens"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 120, "goal": "What are the genres of the movie \"The Shawshank Redemption\"? Please answer me with an array.", "subgoals": [["Crime", "Drama"]], "additional_info": {"answer": ["Crime", "Drama"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 121, "goal": "Who directed the film that won the Palme d'Or at the Cannes Film Festival in 2022? Please answer me with the director's name as a string.", "subgoals": ["Ruben \u00d6stlund"], "additional_info": {"answer": "Ruben \u00d6stlund", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 122, "goal": "Can you provide the keywords associated with the movie \"The Matrix\"? Please answer me with an array.", "subgoals": [["action hero", "artificial intelligence (a.i.)", "complex", "cyberpunk", "dream", "dream world", "dystopia", "fight", "gnosticism", "hacker", "insurgence", "man vs machine", "martial arts", "messiah", "philosophy", "prophecy", "saving the world", "self sacrifice", "simulated reality ", "truth", "virtual reality", "woman director"]], "additional_info": {"answer": ["action hero", "artificial intelligence (a.i.)", "complex", "cyberpunk", "dream", "dream world", "dystopia", "fight", "gnosticism", "hacker", "insurgence", "man vs machine", "martial arts", "messiah", "philosophy", "prophecy", "saving the world", "self sacrifice", "simulated reality ", "truth", "virtual reality", "woman director"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 123, "goal": "Which actress won the Academy Award for Best Supporting Actress for her role in \"12 Years a Slave\"? Please answer me with the actress name as a string.", "subgoals": ["Lupita Nyong'o"], "additional_info": {"answer": "Lupita Nyong'o", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 124, "goal": "What are the production companies behind the movie \"Interstellar\"? Please provide the names as an array.", "subgoals": [["Legendary Pictures", "Lynda Obst Productions", "Syncopy"]], "additional_info": {"answer": ["Legendary Pictures", "Lynda Obst Productions", "Syncopy"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 125, "goal": "Who is the actor known for portraying Batman in the film directed by Christopher Nolan, which explores the theme of justice and morality? Please answer me with the actor's name as a string.", "subgoals": ["Christian Bale"], "additional_info": {"answer": "Christian Bale", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 126, "goal": "Can you provide the biography of the actor who played the lead role in the film adaptation of \"The Great Gatsby\"? Please answer me with a string.", "subgoals": ["Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world."], "additional_info": {"answer": "Leonardo Wilhelm DiCaprio (born November 11, 1974) is an American actor and film producer. Known for his work in biopics and period films, DiCaprio is the recipient of numerous accolades, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.\n\nBorn in Los Angeles, DiCaprio began his career in the late 1980s by appearing in television commercials. In the early 1990s, he had recurring roles in various television shows, such as the sitcom Parenthood, and had his first major film part as author Tobias Wolff in This Boy's Life (1993). At age 19, he received critical acclaim and his first Academy Award and Golden Globe Award nominations for his performance as a developmentally disabled boy in What's Eating Gilbert Grape (1993). He achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).\n\nAfter the latter became the highest-grossing film at the time, he reduced his workload for a few years. In an attempt to shed his image of a romantic hero, DiCaprio sought roles in other genres, including crime drama in Catch Me If You Can (2002) and Gangs of New York (2002); the latter marked the first of his many successful collaborations with director Martin Scorsese. DiCaprio portrayed Howard Hughes in The Aviator (2004) and received acclaim for his performances in the political thriller Blood Diamond (2006), the crime drama The Departed (2006), and the romantic drama Revolutionary Road (2008).\n\nIn the following decade, DiCaprio starred in several high-profile directors' projects, including the science fiction thriller Inception (2010), the western Django Unchained (2012), the biopic The Wolf of Wall Street (2013), the survival drama The Revenant (2015), for which he won an Academy Award and a BAFTA Award for Best Actor in a Leading Role, and the comedy-drama Once Upon a Time in Hollywood (2019), all of which were critical and commercial successes.\n\nDiCaprio is the founder of Appian Way Productions, a production company that has produced some of his films and the documentary series Greensburg (2008\u20132010), and the Leonardo DiCaprio Foundation, a nonprofit organization devoted to promoting environmental awareness. He regularly supports charitable causes and has produced several documentaries on the environment. In 2005, he was named a Commander of the Ordre des Arts et des Lettres for his contributions to the arts, and in 2016, he appeared in Time magazine's 100 most influential people in the world.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 127, "goal": "Could you tell me the release date of the film that marked the directorial debut of Bradley Cooper? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["2018-10-03"], "additional_info": {"answer": "2018-10-03", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 128, "goal": "Which actress starred alongside Tom Hanks in the film directed by Robert Zemeckis, known for its innovative use of motion capture technology? Please answer me with the actress's name as a string.", "subgoals": ["Nona Gaye"], "additional_info": {"answer": "Nona Gaye", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 129, "goal": "Who composed the soundtrack for the movie adaptation of \"The Hunger Games\" series? Please answer me with the composer's name as a string.", "subgoals": ["The information about the composer for 'The Hunger Games' soundtrack is not provided in the crew list."], "additional_info": {"answer": "The information about the composer for 'The Hunger Games' soundtrack is not provided in the crew list.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 130, "goal": "What is the revenue generated by the film directed by Quentin Tarantino, which is set in the post-Civil War era? Please provide the revenue as a number.", "subgoals": [155760117], "additional_info": {"answer": 155760117, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 131, "goal": "What is the birthdate of the actor who portrayed the Joker in the film directed by Todd Phillips? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1974-10-28"], "additional_info": {"answer": "1974-10-28", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 132, "goal": "Can you provide the alternative titles for the movie \"La La Land\"? Please answer me with an array.", "subgoals": [["A\u015f\u0131klar \u015eehri", "La La Land: Kalifornijas sap\u0146i", "La ciudad de las estrellas", "LaLaLand", "\u0633\u0631\u0632\u0645\u06cc\u0646 \u0631\u0648\u06cc\u0627\u0647\u0627", "\u0644\u0627 \u0644\u0627 \u0644\u0646\u062f", "\u0644\u0627\u0644\u0627 \u0644\u0646\u062f", "\u0e19\u0e04\u0e23\u0e14\u0e32\u0e23\u0e32", "\u30e9\u30fb\u30e9\u30fb\u30e9\u30f3\u30c9\uff1a2016", "\ub77c\ub77c\ub79c\ub4dc"]], "additional_info": {"answer": ["A\u015f\u0131klar \u015eehri", "La La Land: Kalifornijas sap\u0146i", "La ciudad de las estrellas", "LaLaLand", "\u0633\u0631\u0632\u0645\u06cc\u0646 \u0631\u0648\u06cc\u0627\u0647\u0627", "\u0644\u0627 \u0644\u0627 \u0644\u0646\u062f", "\u0644\u0627\u0644\u0627 \u0644\u0646\u062f", "\u0e19\u0e04\u0e23\u0e14\u0e32\u0e23\u0e32", "\u30e9\u30fb\u30e9\u30fb\u30e9\u30f3\u30c9\uff1a2016", "\ub77c\ub77c\ub79c\ub4dc"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 133, "goal": "What is the IMDB ID of the actress who played Black Widow in the Marvel Cinematic Universe? Please provide the IMDB ID as a string.", "subgoals": ["nm0424060"], "additional_info": {"answer": "nm0424060", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 134, "goal": "Who directed the film that explores the life of Stephen Hawking, starring Eddie Redmayne in the lead role? Please answer me with the director's name as a string.", "subgoals": ["James Marsh"], "additional_info": {"answer": "James Marsh", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 135, "goal": "What are the genres of the movie \"The Godfather\"? Please provide the genres as an array.", "subgoals": [["Crime", "Drama"]], "additional_info": {"answer": ["Crime", "Drama"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 136, "goal": "Who composed the soundtrack for the film adaptation of \"The Chronicles of Narnia: The Lion, the Witch and the Wardrobe\"? Please answer me with the composer's name as a string.", "subgoals": ["The information is not available."], "additional_info": {"answer": "The information is not available.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 137, "goal": "What is the place of birth of the actor known for his role as Spider-Man in the Marvel Cinematic Universe? Please answer me with the birthplace as a string.", "subgoals": ["Surrey, England, UK"], "additional_info": {"answer": "Surrey, England, UK", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 138, "goal": "Can you provide the keywords associated with the movie \"Fight Club\"? Please answer me with an array.", "subgoals": [["alter ego", "based on novel or book", "breaking the fourth wall", "dissociative identity disorder", "dual identity", "dystopia", "fight", "insomnia", "nihilism", "quitting a job", "rage and hate", "self destructiveness", "split personality", "support group"]], "additional_info": {"answer": ["alter ego", "based on novel or book", "breaking the fourth wall", "dissociative identity disorder", "dual identity", "dystopia", "fight", "insomnia", "nihilism", "quitting a job", "rage and hate", "self destructiveness", "split personality", "support group"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 139, "goal": "Which actress won the Academy Award for Best Actress for her role in the film directed by Guillermo del Toro, known for its unique blend of fantasy and romance? Please answer me with the actress name as a string.", "subgoals": ["Sally Hawkins"], "additional_info": {"answer": "Sally Hawkins", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 140, "goal": "What are the production companies behind the movie \"Jurassic Park\"? Please answer me with an array of strings.", "subgoals": [["Amblin Entertainment", "Universal Pictures"]], "additional_info": {"answer": ["Amblin Entertainment", "Universal Pictures"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 141, "goal": "Who is the director of the film that won the Academy Award for Best Animated Feature in 2022? Please answer me with the names of the directors as a string.", "subgoals": ["Byron Howard and Jared Bush"], "additional_info": {"answer": "Byron Howard and Jared Bush", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 142, "goal": "Could you tell me the biography of the actor who portrayed Tony Stark in the Marvel Cinematic Universe? Please answer me with a string.", "subgoals": ["Robert John Downey Jr. (born April 4, 1966) is an American actor and producer. His career has been characterized by critical and popular success in his youth, followed by a period of substance abuse and legal troubles, before a resurgence of commercial success later in his career. In 2008, Downey was named by Time magazine among the 100 most influential people in the world, and from 2013 to 2015, he was listed by Forbes as Hollywood's highest-paid actor.\n\nAt the age of five, he made his acting debut in his father Robert Downey Sr.'s film Pound in 1970. He subsequently worked with the Brat Pack in the teen films Weird Science (1985) and Less than Zero (1987). In 1992, Downey portrayed the title character in the biopic Chaplin, for which he was nominated for the Academy Award for Best Actor and won a BAFTA Award. Following a stint at the Corcoran Substance Abuse Treatment Facility on drug charges, he joined the TV series Ally McBeal, for which he won a Golden Globe Award. He was fired from the show in the wake of drug charges in 2000 and 2001. He stayed in a court-ordered drug treatment program and has maintained his sobriety since 2003.\n\nInitially, completion bond companies would not insure Downey, until Mel Gibson paid the insurance bond for the 2003 film The Singing Detective. He went on to star in the black comedy Kiss Kiss Bang Bang (2005), the thriller Zodiac (2007), and the action comedy Tropic Thunder (2008); for the latter, he was nominated for an Academy Award for Best Supporting Actor.\n\nDowney gained global recognition for starring as Tony Stark / Iron Man in ten films within the Marvel Cinematic Universe, beginning with Iron Man (2008), and leading up to Avengers: Endgame (2019). He has also played the title character in Guy Ritchie's Sherlock Holmes (2009), which earned him his second Golden Globe, and its sequel, Sherlock Holmes: A Game of Shadows (2011).\n\nIn 2024 he won his first Academy Award for Best Supporting Actor thanks to his work in \"Oppenheimer.\""], "additional_info": {"answer": "Robert John Downey Jr. (born April 4, 1966) is an American actor and producer. His career has been characterized by critical and popular success in his youth, followed by a period of substance abuse and legal troubles, before a resurgence of commercial success later in his career. In 2008, Downey was named by Time magazine among the 100 most influential people in the world, and from 2013 to 2015, he was listed by Forbes as Hollywood's highest-paid actor.\n\nAt the age of five, he made his acting debut in his father Robert Downey Sr.'s film Pound in 1970. He subsequently worked with the Brat Pack in the teen films Weird Science (1985) and Less than Zero (1987). In 1992, Downey portrayed the title character in the biopic Chaplin, for which he was nominated for the Academy Award for Best Actor and won a BAFTA Award. Following a stint at the Corcoran Substance Abuse Treatment Facility on drug charges, he joined the TV series Ally McBeal, for which he won a Golden Globe Award. He was fired from the show in the wake of drug charges in 2000 and 2001. He stayed in a court-ordered drug treatment program and has maintained his sobriety since 2003.\n\nInitially, completion bond companies would not insure Downey, until Mel Gibson paid the insurance bond for the 2003 film The Singing Detective. He went on to star in the black comedy Kiss Kiss Bang Bang (2005), the thriller Zodiac (2007), and the action comedy Tropic Thunder (2008); for the latter, he was nominated for an Academy Award for Best Supporting Actor.\n\nDowney gained global recognition for starring as Tony Stark / Iron Man in ten films within the Marvel Cinematic Universe, beginning with Iron Man (2008), and leading up to Avengers: Endgame (2019). He has also played the title character in Guy Ritchie's Sherlock Holmes (2009), which earned him his second Golden Globe, and its sequel, Sherlock Holmes: A Game of Shadows (2011).\n\nIn 2024 he won his first Academy Award for Best Supporting Actor thanks to his work in \"Oppenheimer.\"", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 143, "goal": "Who is the actor known for playing the lead role in the film adaptation of \"The Fault in Our Stars,\" based on the novel by John Green? Please answer me with the actor's name as a string.", "subgoals": ["Ansel Elgort"], "additional_info": {"answer": "Ansel Elgort", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 144, "goal": "Can you provide the biography of the actress who starred in the film directed by Damien Chazelle, which follows the journey of a young aspiring actress and a jazz musician? Please answer me with the actress's name as a string.", "subgoals": ["Emily Jean \"Emma\" Stone (born November 6, 1988) is an American actress. The recipient of numerous accolades, including two Academy Awards and two Golden Globe Award, she was the world's highest-paid actress in 2017.\n\nBorn and raised in Scottsdale, Arizona, Stone began acting as a child, in a theater production of The Wind in the Willows in 2000. As a teenager, she relocated to Los Angeles with her mother and made her television debut in In Search of the New Partridge Family (2004), a reality show that produced only an unsold pilot. After small television roles, she made her film debut in Superbad (2007), and received positive media attention for her role in Zombieland (2009). The 2010 teen comedy Easy A was Stone's first starring role, earning her nominations for the BAFTA Rising Star Award and a Golden Globe Award for Best Actress.\n\nStone gained wider recognition as Gwen Stacy in the 2012 superhero film The Amazing Spider-Man, and its 2014 sequel. She was nominated for the Academy Award for Best Supporting Actress for playing a recovering drug addict in the black comedy Birdman (2014), and her Broadway debut came in a revival of the musical Cabaret (2014\u20132015). She won the Academy Award for Best Actress for playing an aspiring actress in the romantic musical La La Land (2016). Stone received a third Academy Award nomination for her portrayal of Abigail Masham in the historical comedy-drama The Favourite (2018)."], "additional_info": {"answer": "Emily Jean \"Emma\" Stone (born November 6, 1988) is an American actress. The recipient of numerous accolades, including two Academy Awards and two Golden Globe Award, she was the world's highest-paid actress in 2017.\n\nBorn and raised in Scottsdale, Arizona, Stone began acting as a child, in a theater production of The Wind in the Willows in 2000. As a teenager, she relocated to Los Angeles with her mother and made her television debut in In Search of the New Partridge Family (2004), a reality show that produced only an unsold pilot. After small television roles, she made her film debut in Superbad (2007), and received positive media attention for her role in Zombieland (2009). The 2010 teen comedy Easy A was Stone's first starring role, earning her nominations for the BAFTA Rising Star Award and a Golden Globe Award for Best Actress.\n\nStone gained wider recognition as Gwen Stacy in the 2012 superhero film The Amazing Spider-Man, and its 2014 sequel. She was nominated for the Academy Award for Best Supporting Actress for playing a recovering drug addict in the black comedy Birdman (2014), and her Broadway debut came in a revival of the musical Cabaret (2014\u20132015). She won the Academy Award for Best Actress for playing an aspiring actress in the romantic musical La La Land (2016). Stone received a third Academy Award nomination for her portrayal of Abigail Masham in the historical comedy-drama The Favourite (2018).", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 145, "goal": "What is the budget of the movie directed by Martin Scorsese, which depicts the rise and fall of a real-life stockbroker? Please provide the budget as a number.", "subgoals": [100000000], "additional_info": {"answer": 100000000, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 146, "goal": "Which actor starred alongside Jennifer Lawrence in the film directed by David O. Russell, known for its portrayal of mental illness and family dynamics? Please answer me with the actor's name as a string.", "subgoals": ["Bradley Cooper"], "additional_info": {"answer": "Bradley Cooper", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 147, "goal": "Who composed the soundtrack for the movie adaptation of \"The Girl with the Dragon Tattoo\"? Please answer me with the composer's name as a string.", "subgoals": ["The information about the composer is not listed in the top 10 crew members provided."], "additional_info": {"answer": "The information about the composer is not listed in the top 10 crew members provided.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 148, "goal": "What is the revenue generated by the film directed by Christopher Nolan, which explores the concept of time manipulation? Please provide the revenue as a number.", "subgoals": [825532764], "additional_info": {"answer": 825532764, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 149, "goal": "What is the birthdate of the actor who portrayed the lead character in the film adaptation of \"The Perks of Being a Wallflower\"? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["1992-01-19"], "additional_info": {"answer": "1992-01-19", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 150, "goal": "Can you provide the alternative titles for the movie \"Eternal Sunshine of the Spotless Mind\"? Please list them as an array.", "subgoals": [["Du soleil plein la t\u00eate", "Eterno resplandor de una mente sin recuerdos", "Evig solskinn i et plettfritt sinn", "Karge Meele Igavene S\u00e4ra", "Sil Ba\u015ftan", "\u00a1Olv\u00eddate de m\u00ed!", "\u00c1nh D\u01b0\u01a1ng V\u0129nh C\u1eedu C\u1ee7a T\u00e2m H\u1ed3n Thanh Khi\u1ebft", "\u65e0\u6687\u5fc3\u7075\u7684\u6c38\u6052\u9633\u5149", "\u66a7\u66a7\u5185\u542b\u5149", "\u7075\u5149\u4e4d\u73b0", "\u7f8e\u4e3d\u5fc3\u7075\u7684\u6c38\u6052\u9633\u5149", "\uc774\ud130\ub110 \uc120\uc0e4\uc778"]], "additional_info": {"answer": ["Du soleil plein la t\u00eate", "Eterno resplandor de una mente sin recuerdos", "Evig solskinn i et plettfritt sinn", "Karge Meele Igavene S\u00e4ra", "Sil Ba\u015ftan", "\u00a1Olv\u00eddate de m\u00ed!", "\u00c1nh D\u01b0\u01a1ng V\u0129nh C\u1eedu C\u1ee7a T\u00e2m H\u1ed3n Thanh Khi\u1ebft", "\u65e0\u6687\u5fc3\u7075\u7684\u6c38\u6052\u9633\u5149", "\u66a7\u66a7\u5185\u542b\u5149", "\u7075\u5149\u4e4d\u73b0", "\u7f8e\u4e3d\u5fc3\u7075\u7684\u6c38\u6052\u9633\u5149", "\uc774\ud130\ub110 \uc120\uc0e4\uc778"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 151, "goal": "What is the IMDB ID of the actress who played Katniss Everdeen in \"The Hunger Games\" film series? Please provide the IMDB ID as a string.", "subgoals": ["nm2225369"], "additional_info": {"answer": "nm2225369", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 152, "goal": "Who directed the film that follows the journey of a young boy who survives a shipwreck and is stranded on a lifeboat with a Bengal tiger? Please answer me with the director's name as a string.", "subgoals": ["Ang Lee"], "additional_info": {"answer": "Ang Lee", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 153, "goal": "What are the genres of the movie \"Forrest Gump\"? Please provide the genres as an array.", "subgoals": [["Comedy", "Drama", "Romance"]], "additional_info": {"answer": ["Comedy", "Drama", "Romance"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 154, "goal": "Who composed the soundtrack for the film adaptation of \"The Lord of the Rings: The Fellowship of the Ring\"? Please answer me with the composer's name as a string.", "subgoals": ["Howard Shore"], "additional_info": {"answer": "Howard Shore", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 155, "goal": "What is the place of birth of the actor known for his role as Thor in the Marvel Cinematic Universe? Please answer me with the birthplace as a string.", "subgoals": ["Melbourne, Victoria, Australia"], "additional_info": {"answer": "Melbourne, Victoria, Australia", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 156, "goal": "Can you provide the keywords associated with the movie \"The Dark Knight\"? Please answer me with an array.", "subgoals": [["anti hero", "anti villain", "based on comic", "chaos", "crime fighter", "criminal mastermind", "district attorney", "joker", "neo-noir", "organized crime", "sadism", "scarecrow", "secret identity", "super power", "super villain", "superhero", "tragic hero", "vigilante"]], "additional_info": {"answer": ["anti hero", "anti villain", "based on comic", "chaos", "crime fighter", "criminal mastermind", "district attorney", "joker", "neo-noir", "organized crime", "sadism", "scarecrow", "secret identity", "super power", "super villain", "superhero", "tragic hero", "vigilante"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 157, "goal": "What are the production companies behind the movie \"The Avengers\"? Please answer me with the name of the production company as a string.", "subgoals": ["Marvel Studios"], "additional_info": {"answer": "Marvel Studios", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 158, "goal": "Who is the director of the film that won the Academy Award for Best Foreign Language Film in 2021? Please answer me with the director's name as a string.", "subgoals": ["Thomas Vinterberg"], "additional_info": {"answer": "Thomas Vinterberg", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 159, "goal": "Could you tell me the biography of the actor who portrayed Harry Potter in the film series based on the novels by J.K. Rowling? Please answer me with a brief summary.", "subgoals": ["Daniel Jacob Radcliffe (born 23 July 1989) is an English actor. He rose to fame at age twelve, when he began portraying Harry Potter in the film series of the same name; and has held various other film and theatre roles. Over his career, Radcliffe has received various awards and nominations.\n\nRadcliffe made his acting debut at age 10 in the BBC One television film David Copperfield (1999), followed by his feature film debut in The Tailor of Panama (2001). The same year, he starred as Harry Potter in the film adaptation of the J.K. Rowling fantasy novel, Harry Potter and the Philosopher's Stone. Over the next decade, he played the eponymous role in seven sequels, culminating with Harry Potter and the Deathly Hallows \u2013 Part 2 (2011). During this period, he became one of the world's highest-paid actors and gained worldwide fame, popularity, and critical acclaim.\n\nFollowing the success of Harry Potter, Radcliffe starred in the romantic comedy What If? (2013), and played the lawyer Arthur Kipps in the horror film The Woman in Black (2012), poet Allen Ginsberg in the drama film Kill Your Darlings (2013), Igor in the science-fiction horror film Victor Frankenstein (2015), a sentient corpse in the comedy-drama film Swiss Army Man (2016), technological prodigy Walter Mabry in the heist thriller film Now You See Me 2 (2016), and FBI agent Nate Foster in the critically acclaimed thriller film Imperium (2016). Since 2019, he has starred in the TBS anthology series Miracle Workers. In 2022, he starred in the action comedy The Lost City and portrayed Weird Al Yankovic in Weird: The Al Yankovic Story.\n\nRadcliffe branched out to stage acting in 2007, starring in the West End and Broadway productions of Equus. From 2011 to 2012 he portrayed J. Pierrepont Finch in the Broadway revival of the musical How to Succeed in Business Without Really Trying. He continued in Martin McDonagh's dark comedy The Cripple of Inishmaan (2013-2014) in the West End and Broadway and a revival of Tom Stoppard's play Rosencrantz and Guildenstern Are Dead (2017) at The Old Vic. He also starred in the satirical plays Privacy (2016) and The Lifespan of a Fact (2018), respectively off and on Broadway. In 2022 starred in the New York Theatre Workshop revival of Stephen Sondheim's Merrily We Roll Along."], "additional_info": {"answer": "Daniel Jacob Radcliffe (born 23 July 1989) is an English actor. He rose to fame at age twelve, when he began portraying Harry Potter in the film series of the same name; and has held various other film and theatre roles. Over his career, Radcliffe has received various awards and nominations.\n\nRadcliffe made his acting debut at age 10 in the BBC One television film David Copperfield (1999), followed by his feature film debut in The Tailor of Panama (2001). The same year, he starred as Harry Potter in the film adaptation of the J.K. Rowling fantasy novel, Harry Potter and the Philosopher's Stone. Over the next decade, he played the eponymous role in seven sequels, culminating with Harry Potter and the Deathly Hallows \u2013 Part 2 (2011). During this period, he became one of the world's highest-paid actors and gained worldwide fame, popularity, and critical acclaim.\n\nFollowing the success of Harry Potter, Radcliffe starred in the romantic comedy What If? (2013), and played the lawyer Arthur Kipps in the horror film The Woman in Black (2012), poet Allen Ginsberg in the drama film Kill Your Darlings (2013), Igor in the science-fiction horror film Victor Frankenstein (2015), a sentient corpse in the comedy-drama film Swiss Army Man (2016), technological prodigy Walter Mabry in the heist thriller film Now You See Me 2 (2016), and FBI agent Nate Foster in the critically acclaimed thriller film Imperium (2016). Since 2019, he has starred in the TBS anthology series Miracle Workers. In 2022, he starred in the action comedy The Lost City and portrayed Weird Al Yankovic in Weird: The Al Yankovic Story.\n\nRadcliffe branched out to stage acting in 2007, starring in the West End and Broadway productions of Equus. From 2011 to 2012 he portrayed J. Pierrepont Finch in the Broadway revival of the musical How to Succeed in Business Without Really Trying. He continued in Martin McDonagh's dark comedy The Cripple of Inishmaan (2013-2014) in the West End and Broadway and a revival of Tom Stoppard's play Rosencrantz and Guildenstern Are Dead (2017) at The Old Vic. He also starred in the satirical plays Privacy (2016) and The Lifespan of a Fact (2018), respectively off and on Broadway. In 2022 starred in the New York Theatre Workshop revival of Stephen Sondheim's Merrily We Roll Along.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 160, "goal": "What is the television debut directing of the director of 'The French Dispatch'? Please answer me with the specific work or state if it is not specified.", "subgoals": ["Not specified"], "additional_info": {"answer": "Not specified", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 161, "goal": "Which year did the director of the movie 'Knives Out' win the America Award for Outstanding Directing \u2013 Drama Series? Please answer me with a specific year or state if it's not available.", "subgoals": ["Not available"], "additional_info": {"answer": "Not available", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 162, "goal": "May I ask, in which movie did Meryl Streep first gain attention? Please answer me with the movie name as a string.", "subgoals": ["The Deer Hunter"], "additional_info": {"answer": "The Deer Hunter", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 163, "goal": "Who is the director of the film that Meryl Streep first gained attention? Please answer me with the director's name as a string.", "subgoals": ["Michael Cimino"], "additional_info": {"answer": "Michael Cimino", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 164, "goal": "In the movie for which Halle Berry won the Oscar for Best Actress, what role did Ian McKellen play? Please answer me with the character name as a string.", "subgoals": ["Ian McKellen did not have a role in 'Monster's Ball'."], "additional_info": {"answer": "Ian McKellen did not have a role in 'Monster's Ball'.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 165, "goal": "I am very interested in the actor who plays Arthur Curry in Aquaman. What is his IMDB ID? Please provide the answer as a string.", "subgoals": ["nm0597388"], "additional_info": {"answer": "nm0597388", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 166, "goal": "Meryl Streep won the Academy Award for Best Actress in 2012. I would like to see the movie for which she won the award. What is its official Japanese title? Please provide me with the official Japanese title as a string.", "subgoals": ["The official Japanese title for 'The Iron Lady' is not available."], "additional_info": {"answer": "The official Japanese title for 'The Iron Lady' is not available.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 167, "goal": "Cate Blanchett won the Academy Award for Best Actress in 2014. Please give you an overview in German about the movie for which she won the award. Please provide the overview as a string.", "subgoals": ["Nach der Verhaftung ihres Gatten wegen Investmentbetrugs fliegt Society-Lady Jasmine aus ihrem s\u00fcndteuren Manhattan-Luxus-Appartement und findet Unterschlupf in der kleinen Mietwohnung bei ihrer Adoptivschwester in San Francisco. Mangels Ausbildung und Computerkenntnissen findet sie keinen ihr genehmen Job. Als sie einen reichen und von ihr faszinierten Diplomaten kennen lernt, hofft das Nervenb\u00fcndel auf einen Neuanfang."], "additional_info": {"answer": "Nach der Verhaftung ihres Gatten wegen Investmentbetrugs fliegt Society-Lady Jasmine aus ihrem s\u00fcndteuren Manhattan-Luxus-Appartement und findet Unterschlupf in der kleinen Mietwohnung bei ihrer Adoptivschwester in San Francisco. Mangels Ausbildung und Computerkenntnissen findet sie keinen ihr genehmen Job. Als sie einen reichen und von ihr faszinierten Diplomaten kennen lernt, hofft das Nervenb\u00fcndel auf einen Neuanfang.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 168, "goal": "Who composed the soundtrack for the movie adaptation of \"Harry Potter and the Philosopher's Stone\"? Please answer me with the composer's name as a string.", "subgoals": ["The information about the composer is not listed in the top 10 crew members provided. Please check the full movie credits for this information."], "additional_info": {"answer": "The information about the composer is not listed in the top 10 crew members provided. Please check the full movie credits for this information.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 169, "goal": "Can you provide the biography of the actor who played Legolas in \"The Lord of the Rings\" film series? Please answer me with a brief summary.", "subgoals": ["Orlando Jonathan Blanchard Copeland Bloom (born 13 January 1977) is an English actor. He made his breakthrough as the character Legolas in The Lord of the Rings film series, a role he reprised in The Hobbit film series. He gained further notice appearing in epic fantasy, historical, and adventure films, notably as Will Turner in the Pirates of the Caribbean film series.\n\nBloom appeared in Hollywood films such as Paris in Troy (2004) and Balian de Ibelin in Kingdom of Heaven (2005). He stars in the Amazon Prime Video series Carnival Row (2019\u2013present).\n\nHe made his professional stage debut in In Celebration at the Duke of York's Theatre in the West End in 2007 and starred in a Broadway adaption of Romeo and Juliet in 2013. In 2009, Bloom was named a UNICEF Goodwill Ambassador. In 2015 he received the BAFTA Britannia Humanitarian Award."], "additional_info": {"answer": "Orlando Jonathan Blanchard Copeland Bloom (born 13 January 1977) is an English actor. He made his breakthrough as the character Legolas in The Lord of the Rings film series, a role he reprised in The Hobbit film series. He gained further notice appearing in epic fantasy, historical, and adventure films, notably as Will Turner in the Pirates of the Caribbean film series.\n\nBloom appeared in Hollywood films such as Paris in Troy (2004) and Balian de Ibelin in Kingdom of Heaven (2005). He stars in the Amazon Prime Video series Carnival Row (2019\u2013present).\n\nHe made his professional stage debut in In Celebration at the Duke of York's Theatre in the West End in 2007 and starred in a Broadway adaption of Romeo and Juliet in 2013. In 2009, Bloom was named a UNICEF Goodwill Ambassador. In 2015 he received the BAFTA Britannia Humanitarian Award.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 170, "goal": "Could you provide the Mandarin translation of the overview for the film \"Your Name\"? Please answer me with a string.", "subgoals": ["\u5728\u8fdc\u79bb\u5927\u90fd\u4f1a\u7684\u5c0f\u5c71\u6751\uff0c\u4f4f\u7740\u5deb\u5973\u4e16\u5bb6\u51fa\u8eab\u7684\u9ad8\u4e2d\u5973\u5b69\u5bab\u6c34\u4e09\u53f6\uff08\u4e0a\u767d\u77f3\u840c\u97f3 \u914d\u97f3\uff09\u3002\u6821\u56ed\u548c\u5bb6\u5ead\u7684\u539f\u56e0\u672c\u5c31\u8ba9\u5979\u5145\u6ee1\u70e6\u607c\uff0c\u800c\u8fd1\u4e00\u6bb5\u65f6\u95f4\u53d1\u751f\u7684\u5947\u602a\u4e8b\u4ef6\uff0c\u53c8\u8ba9\u4e09\u53f6\u6478\u4e0d\u6e05\u5934\u8111\u3002\u4e0d\u77e5\u4ece\u4f55\u65f6\u8d77\uff0c\u4e09\u53f6\u5728\u68a6\u4e2d\u5c31\u4f1a\u53d8\u6210\u4e00\u4e2a\u4f4f\u5728\u4e1c\u4eac\u7684\u9ad8\u4e2d\u7537\u5b69\u3002\u90a3\u91cc\u6709\u964c\u751f\u7684\u540c\u5b66\u548c\u670b\u53cb\uff0c\u6709\u4eb2\u5207\u7684\u524d\u8f88\u548c\u7e41\u534e\u7684\u8857\u9053\uff0c\u4e00\u5207\u90fd\u662f\u5982\u6b64\u8bf1\u4eba\u800c\u771f\u5b9e\u3002\u53e6\u4e00\u65b9\u9762\uff0c\u4f4f\u5728\u4e1c\u4eac\u7684\u9ad8\u4e2d\u7537\u5b69\u7acb\u82b1\u6cf7\uff08\u795e\u6728\u9686\u4e4b\u4ecb \u914d\u97f3\uff09\u5219\u603b\u5728\u68a6\u91cc\u6765\u5230\u964c\u751f\u7684\u5c0f\u5c71\u6751\uff0c\u4ee5\u5973\u5b69\u5b50\u7684\u8eab\u4efd\u8fc7\u7740\u5168\u65b0\u7684\u751f\u6d3b\u3002\u8bb8\u662f\u53d7\u90a3\u9897\u795e\u79d8\u5f57\u661f\u7684\u5f71\u54cd\uff0c\u7acb\u82b1\u548c\u4e09\u53f6\u5728\u68a6\u4e2d\u4ea4\u6362\u4e86\u8eab\u4efd\u3002\u4ed6\u4eec\u4ee5\u4ed6\u8005\u7684\u89d2\u5ea6\u4f53\u9a8c\u7740\u5bf9\u65b9\u7684\u4eba\u751f\uff0c\u8fd9\u671f\u95f4\u6709\u6124\u6012\u3001\u6709\u6b22\u7b11\u4e5f\u6709\u6696\u5fc3\u3002\u53ea\u662f\u4e24\u4eba\u5e76\u4e0d\u77e5\u9053\uff0c\u8eab\u4efd\u4ea4\u6362\u7684\u80cc\u540e\u9690\u85cf\u7740\u91cd\u5927\u800c\u9525\u5fc3\u7684\u79d8\u5bc6\u2026\u2026"], "additional_info": {"answer": "\u5728\u8fdc\u79bb\u5927\u90fd\u4f1a\u7684\u5c0f\u5c71\u6751\uff0c\u4f4f\u7740\u5deb\u5973\u4e16\u5bb6\u51fa\u8eab\u7684\u9ad8\u4e2d\u5973\u5b69\u5bab\u6c34\u4e09\u53f6\uff08\u4e0a\u767d\u77f3\u840c\u97f3 \u914d\u97f3\uff09\u3002\u6821\u56ed\u548c\u5bb6\u5ead\u7684\u539f\u56e0\u672c\u5c31\u8ba9\u5979\u5145\u6ee1\u70e6\u607c\uff0c\u800c\u8fd1\u4e00\u6bb5\u65f6\u95f4\u53d1\u751f\u7684\u5947\u602a\u4e8b\u4ef6\uff0c\u53c8\u8ba9\u4e09\u53f6\u6478\u4e0d\u6e05\u5934\u8111\u3002\u4e0d\u77e5\u4ece\u4f55\u65f6\u8d77\uff0c\u4e09\u53f6\u5728\u68a6\u4e2d\u5c31\u4f1a\u53d8\u6210\u4e00\u4e2a\u4f4f\u5728\u4e1c\u4eac\u7684\u9ad8\u4e2d\u7537\u5b69\u3002\u90a3\u91cc\u6709\u964c\u751f\u7684\u540c\u5b66\u548c\u670b\u53cb\uff0c\u6709\u4eb2\u5207\u7684\u524d\u8f88\u548c\u7e41\u534e\u7684\u8857\u9053\uff0c\u4e00\u5207\u90fd\u662f\u5982\u6b64\u8bf1\u4eba\u800c\u771f\u5b9e\u3002\u53e6\u4e00\u65b9\u9762\uff0c\u4f4f\u5728\u4e1c\u4eac\u7684\u9ad8\u4e2d\u7537\u5b69\u7acb\u82b1\u6cf7\uff08\u795e\u6728\u9686\u4e4b\u4ecb \u914d\u97f3\uff09\u5219\u603b\u5728\u68a6\u91cc\u6765\u5230\u964c\u751f\u7684\u5c0f\u5c71\u6751\uff0c\u4ee5\u5973\u5b69\u5b50\u7684\u8eab\u4efd\u8fc7\u7740\u5168\u65b0\u7684\u751f\u6d3b\u3002\u8bb8\u662f\u53d7\u90a3\u9897\u795e\u79d8\u5f57\u661f\u7684\u5f71\u54cd\uff0c\u7acb\u82b1\u548c\u4e09\u53f6\u5728\u68a6\u4e2d\u4ea4\u6362\u4e86\u8eab\u4efd\u3002\u4ed6\u4eec\u4ee5\u4ed6\u8005\u7684\u89d2\u5ea6\u4f53\u9a8c\u7740\u5bf9\u65b9\u7684\u4eba\u751f\uff0c\u8fd9\u671f\u95f4\u6709\u6124\u6012\u3001\u6709\u6b22\u7b11\u4e5f\u6709\u6696\u5fc3\u3002\u53ea\u662f\u4e24\u4eba\u5e76\u4e0d\u77e5\u9053\uff0c\u8eab\u4efd\u4ea4\u6362\u7684\u80cc\u540e\u9690\u85cf\u7740\u91cd\u5927\u800c\u9525\u5fc3\u7684\u79d8\u5bc6\u2026\u2026", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 171, "goal": "May I ask for the German translation of the description of the movie \"El Laberinto del Fauno\"? Please provide the translation as a string.", "subgoals": ["Spanien, 1944: Nachdem der B\u00fcrgerkrieg schon seit f\u00fcnf Jahren vorbei ist, k\u00e4mpfen in den Bergen Nordspaniens immer noch republikanische Partisanen gegen das faschistische Franco-Regime. Die elfj\u00e4hrige Ofelia zieht mit ihrer schwangeren Mutter Carmen in die umk\u00e4mpfte Region, da ihr Stiefvater Hauptmann Vidal seine neue Frau bei sich haben will, wenn sie das Kind bekommt. Der sadistische Hauptmann ist von der Regierung mit der Zerschlagung des Widerstandes beauftragt worden und geht mit grausamen Methoden gegen die Rebellen und vermeintliche Sympathisanten vor. Ofelia fl\u00fcchtet sich w\u00e4hrenddessen in die Fantasiewelt ihrer B\u00fccher, die von Elfen und andere Kreaturen bev\u00f6lkert ist. Eines Tages erscheint ihr in einem Labyrinth in der N\u00e4he des Landsitzes ein Pan, der ihr offenbart, dass sie in Wirklichkeit eine K\u00f6nigstochter aus einem unterirdischen K\u00f6nigreich sei. Er erlegt Ofelia drei Mutproben auf, die sie bestehen muss, um in das Reich ihres Vaters zur\u00fcckkehren zu k\u00f6nnen\u2026"], "additional_info": {"answer": "Spanien, 1944: Nachdem der B\u00fcrgerkrieg schon seit f\u00fcnf Jahren vorbei ist, k\u00e4mpfen in den Bergen Nordspaniens immer noch republikanische Partisanen gegen das faschistische Franco-Regime. Die elfj\u00e4hrige Ofelia zieht mit ihrer schwangeren Mutter Carmen in die umk\u00e4mpfte Region, da ihr Stiefvater Hauptmann Vidal seine neue Frau bei sich haben will, wenn sie das Kind bekommt. Der sadistische Hauptmann ist von der Regierung mit der Zerschlagung des Widerstandes beauftragt worden und geht mit grausamen Methoden gegen die Rebellen und vermeintliche Sympathisanten vor. Ofelia fl\u00fcchtet sich w\u00e4hrenddessen in die Fantasiewelt ihrer B\u00fccher, die von Elfen und andere Kreaturen bev\u00f6lkert ist. Eines Tages erscheint ihr in einem Labyrinth in der N\u00e4he des Landsitzes ein Pan, der ihr offenbart, dass sie in Wirklichkeit eine K\u00f6nigstochter aus einem unterirdischen K\u00f6nigreich sei. Er erlegt Ofelia drei Mutproben auf, die sie bestehen muss, um in das Reich ihres Vaters zur\u00fcckkehren zu k\u00f6nnen\u2026", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 172, "goal": "What is the German translation of the overview for the film \"Cinema Paradiso\"? Please provide the translation as a string.", "subgoals": ["Alfredo ist tot. Das Paradiso ist tot \u2013 es lebe das Kino. Cinema Paradiso ist die Geschichte des skurrilen Filmvorf\u00fchrers Alfredo zu dem kleinen Jungen Toto. In Rom wird Toto ein ber\u00fchmter Regisseur und kehrt erst wieder in seine sizilianische Heimat zur\u00fcck, als Alfredo stirbt und das sch\u00f6ne, alte Provinzkino abgerissen wird. Alfredos Verm\u00e4chtnis sind all die zensierten Szenen, die er in vielen Jahren aus so vielen Filmen herausschneiden musste."], "additional_info": {"answer": "Alfredo ist tot. Das Paradiso ist tot \u2013 es lebe das Kino. Cinema Paradiso ist die Geschichte des skurrilen Filmvorf\u00fchrers Alfredo zu dem kleinen Jungen Toto. In Rom wird Toto ein ber\u00fchmter Regisseur und kehrt erst wieder in seine sizilianische Heimat zur\u00fcck, als Alfredo stirbt und das sch\u00f6ne, alte Provinzkino abgerissen wird. Alfredos Verm\u00e4chtnis sind all die zensierten Szenen, die er in vielen Jahren aus so vielen Filmen herausschneiden musste.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 173, "goal": "Could you give me the Russian translation of the description for the movie \"Mission \u00abSky\u00bb\"? Please provide the translation as a string.", "subgoals": ["\u041f\u043e\u0434\u043f\u043e\u043b\u043a\u043e\u0432\u043d\u0438\u043a \u0421\u043e\u0448\u043d\u0438\u043a\u043e\u0432 \u0438 \u043a\u0430\u043f\u0438\u0442\u0430\u043d \u041c\u0443\u0440\u0430\u0432\u044c\u0435\u0432 \u2014 \u0434\u0432\u0430 \u0440\u0430\u0437\u043d\u044b\u0445 \u0445\u0430\u0440\u0430\u043a\u0442\u0435\u0440\u0430, \u0434\u0432\u0435 \u0440\u0430\u0437\u043d\u044b\u0435 \u0441\u0443\u0434\u044c\u0431\u044b, \u043a\u043e\u0442\u043e\u0440\u044b\u043c \u0441\u0443\u0436\u0434\u0435\u043d\u043e \u0431\u0443\u0434\u0435\u0442 \u0441\u043e\u0439\u0442\u0438\u0441\u044c \u043d\u0430 \u0432\u043e\u0435\u043d\u043d\u043e\u0439 \u0431\u0430\u0437\u0435 \u0425\u043c\u0435\u0439\u043c\u0438\u043c. \u0412\u043e \u0432\u0440\u0435\u043c\u044f \u0431\u043e\u0435\u0432\u043e\u0433\u043e \u0432\u044b\u043b\u0435\u0442\u0430 \u0441\u0430\u043c\u043e\u043b\u0435\u0442 \u0421\u043e\u0448\u043d\u0438\u043a\u043e\u0432\u0430 \u0431\u044b\u043b \u0441\u0431\u0438\u0442 \u0442\u0443\u0440\u0435\u0446\u043a\u0438\u043c \u0438\u0441\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u0435\u043c. \u042d\u0442\u043e \u0441\u043e\u0431\u044b\u0442\u0438\u0435 \u043e\u0441\u0432\u0435\u0449\u0430\u043b\u0438 \u0432\u0441\u0435 \u043c\u0438\u0440\u043e\u0432\u044b\u0435 \u0421\u041c\u0418, \u0430 \u0437\u0430 \u0445\u043e\u0434\u043e\u043c \u0441\u043f\u0430\u0441\u0430\u0442\u0435\u043b\u044c\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0435\u0434\u0438\u043b\u0430 \u0432\u0441\u044f \u0420\u043e\u0441\u0441\u0438\u044f."], "additional_info": {"answer": "\u041f\u043e\u0434\u043f\u043e\u043b\u043a\u043e\u0432\u043d\u0438\u043a \u0421\u043e\u0448\u043d\u0438\u043a\u043e\u0432 \u0438 \u043a\u0430\u043f\u0438\u0442\u0430\u043d \u041c\u0443\u0440\u0430\u0432\u044c\u0435\u0432 \u2014 \u0434\u0432\u0430 \u0440\u0430\u0437\u043d\u044b\u0445 \u0445\u0430\u0440\u0430\u043a\u0442\u0435\u0440\u0430, \u0434\u0432\u0435 \u0440\u0430\u0437\u043d\u044b\u0435 \u0441\u0443\u0434\u044c\u0431\u044b, \u043a\u043e\u0442\u043e\u0440\u044b\u043c \u0441\u0443\u0436\u0434\u0435\u043d\u043e \u0431\u0443\u0434\u0435\u0442 \u0441\u043e\u0439\u0442\u0438\u0441\u044c \u043d\u0430 \u0432\u043e\u0435\u043d\u043d\u043e\u0439 \u0431\u0430\u0437\u0435 \u0425\u043c\u0435\u0439\u043c\u0438\u043c. \u0412\u043e \u0432\u0440\u0435\u043c\u044f \u0431\u043e\u0435\u0432\u043e\u0433\u043e \u0432\u044b\u043b\u0435\u0442\u0430 \u0441\u0430\u043c\u043e\u043b\u0435\u0442 \u0421\u043e\u0448\u043d\u0438\u043a\u043e\u0432\u0430 \u0431\u044b\u043b \u0441\u0431\u0438\u0442 \u0442\u0443\u0440\u0435\u0446\u043a\u0438\u043c \u0438\u0441\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u0435\u043c. \u042d\u0442\u043e \u0441\u043e\u0431\u044b\u0442\u0438\u0435 \u043e\u0441\u0432\u0435\u0449\u0430\u043b\u0438 \u0432\u0441\u0435 \u043c\u0438\u0440\u043e\u0432\u044b\u0435 \u0421\u041c\u0418, \u0430 \u0437\u0430 \u0445\u043e\u0434\u043e\u043c \u0441\u043f\u0430\u0441\u0430\u0442\u0435\u043b\u044c\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0435\u0434\u0438\u043b\u0430 \u0432\u0441\u044f \u0420\u043e\u0441\u0441\u0438\u044f.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 174, "goal": "May I request the Japanese translation of the overview for the film \"The Garden of Words\"? Please provide the translation as a string.", "subgoals": ["\u5b66\u6821\u3092\u30b5\u30dc\u308a\u3001\u516c\u5712\u306e\u65e5\u672c\u5ead\u5712\u3067\u9774\u306e\u30b9\u30b1\u30c3\u30c1\u3092\u63cf\u304f\u9ad8\u6821\u751f\u306e\u30bf\u30ab\u30aa\u3002\u9774\u8077\u4eba\u3092\u76ee\u6307\u3059\u30bf\u30ab\u30aa\u306f\u305d\u3053\u3067\u3001\u7f36\u30d3\u30fc\u30eb\u3092\u98f2\u3080\u5973\u6027\u3001\u30e6\u30ad\u30ce\u3068\u51fa\u4f1a\u3046\u3002\u30e6\u30ad\u30ce\u306f\u30bf\u30ab\u30aa\u306b\u300c\u307e\u305f\u4f1a\u3046\u304b\u3082\u306d\u3002\u96e8\u304c\u964d\u3063\u305f\u3089\u300d\u3068\u544a\u3052\u3001\u305d\u306e\u5834\u3092\u5f8c\u306b\u3057\u305f\u3002\u3053\u3046\u3057\u3066\u4e8c\u4eba\u306f\u7d04\u675f\u3082\u306a\u3044\u307e\u307e\u3001\u96e8\u306e\u65e5\u306e\u516c\u5712\u3067\u9022\u702c\u3092\u91cd\u306d\u308b\u3088\u3046\u306b\u306a\u308b\u3002\u6b69\u304d\u65b9\u3092\u5fd8\u308c\u305f\u3068\u3044\u3046\u30e6\u30ad\u30ce\u306e\u305f\u3081\u3001\u30bf\u30ab\u30aa\u306f\u9774\u3092\u4f5c\u308d\u3046\u3068\u3059\u308b\u306e\u3060\u3063\u305f\u3002"], "additional_info": {"answer": "\u5b66\u6821\u3092\u30b5\u30dc\u308a\u3001\u516c\u5712\u306e\u65e5\u672c\u5ead\u5712\u3067\u9774\u306e\u30b9\u30b1\u30c3\u30c1\u3092\u63cf\u304f\u9ad8\u6821\u751f\u306e\u30bf\u30ab\u30aa\u3002\u9774\u8077\u4eba\u3092\u76ee\u6307\u3059\u30bf\u30ab\u30aa\u306f\u305d\u3053\u3067\u3001\u7f36\u30d3\u30fc\u30eb\u3092\u98f2\u3080\u5973\u6027\u3001\u30e6\u30ad\u30ce\u3068\u51fa\u4f1a\u3046\u3002\u30e6\u30ad\u30ce\u306f\u30bf\u30ab\u30aa\u306b\u300c\u307e\u305f\u4f1a\u3046\u304b\u3082\u306d\u3002\u96e8\u304c\u964d\u3063\u305f\u3089\u300d\u3068\u544a\u3052\u3001\u305d\u306e\u5834\u3092\u5f8c\u306b\u3057\u305f\u3002\u3053\u3046\u3057\u3066\u4e8c\u4eba\u306f\u7d04\u675f\u3082\u306a\u3044\u307e\u307e\u3001\u96e8\u306e\u65e5\u306e\u516c\u5712\u3067\u9022\u702c\u3092\u91cd\u306d\u308b\u3088\u3046\u306b\u306a\u308b\u3002\u6b69\u304d\u65b9\u3092\u5fd8\u308c\u305f\u3068\u3044\u3046\u30e6\u30ad\u30ce\u306e\u305f\u3081\u3001\u30bf\u30ab\u30aa\u306f\u9774\u3092\u4f5c\u308d\u3046\u3068\u3059\u308b\u306e\u3060\u3063\u305f\u3002", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 175, "goal": "What is the Dutch translation of the overview for the film \"The Dragon That Wasn't (Or Was He)\"? Please provide the translation as a string.", "subgoals": ["Slot Bommelstein wordt opgeschrikt door een jonge Zwelbast, een draakje dat bij boosheid opzwelt tot gigantische afmetingen en veel schade berokkent. Als een pleegvader ontfermt heer Bommel zich over het beestje dat Zwelgje wordt genoemd, met alle gevolgen van dien."], "additional_info": {"answer": "Slot Bommelstein wordt opgeschrikt door een jonge Zwelbast, een draakje dat bij boosheid opzwelt tot gigantische afmetingen en veel schade berokkent. Als een pleegvader ontfermt heer Bommel zich over het beestje dat Zwelgje wordt genoemd, met alle gevolgen van dien.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 176, "goal": "Could you give me the Japanese translation of the description for the movie \"5 Centimeters per Second\"?", "subgoals": ["\u6771\u4eac\u306e\u5c0f\u5b66\u751f\u30fb\u9060\u91ce\u8cb4\u6a39\u3068\u7be0\u539f\u660e\u91cc\u306f\u304a\u4e92\u3044\u306b\u5bfe\u3059\u308b\u300c\u4ed6\u4eba\u306b\u306f\u5206\u3089\u306a\u3044\u7279\u5225\u306a\u60f3\u3044\u300d\u3092\u62b1\u3048\u3066\u3044\u305f\u3002\u3057\u304b\u3057\u5c0f\u5b66\u6821\u5352\u696d\u3068\u540c\u6642\u306b\u660e\u91cc\u306f\u6803\u6728\u3078\u8ee2\u6821\u3057\u3066\u3057\u307e\u3044\u3001\u305d\u308c\u304d\u308a\u4f1a\u3046\u3053\u3068\u304c\u7121\u304f\u306a\u3063\u3066\u3057\u307e\u3046\u3002\u8cb4\u6a39\u304c\u4e2d\u5b66\u306b\u5165\u5b66\u3057\u3066\u534a\u5e74\u304c\u7d4c\u904e\u3057\u305f\u590f\u306e\u3042\u308b\u65e5\u3001\u6803\u6728\u306e\u660e\u91cc\u304b\u3089\u624b\u7d19\u304c\u5c4a\u304f\u3002\u305d\u308c\u3092\u304d\u3063\u304b\u3051\u306b\u3001\u6587\u901a\u3092\u91cd\u306d\u308b\u3088\u3046\u306b\u306a\u308b2\u4eba\u3002\u3057\u304b\u3057\u305d\u306e\u5e74\u306e\u51ac\u306b\u3001\u4eca\u5ea6\u306f\u8cb4\u6a39\u304c\u9e7f\u5150\u5cf6\u3078\u8ee2\u6821\u3059\u308b\u3053\u3068\u304c\u6c7a\u307e\u3063\u305f\u3002\u9e7f\u5150\u5cf6\u3068\u6803\u6728\u3067\u306f\u7d76\u671b\u7684\u306b\u9060\u3044\u3002\u300c\u3082\u3046\u4e8c\u5ea6\u3068\u4f1a\u3048\u306a\u304f\u306a\u308b\u304b\u3082\u3057\u308c\u306a\u3044\u2026\u300d\u305d\u3046\u601d\u3063\u305f\u8cb4\u6a39\u306f\u3001\u660e\u91cc\u306b\u4f1a\u3044\u306b\u884c\u304f\u6c7a\u610f\u3092\u3059\u308b\u3002\u3057\u304b\u3057\u305d\u306e\u7d04\u675f\u306e\u65e5\u3001\u95a2\u6771\u3067\u306f\u5927\u96ea\u3068\u306a\u3063\u305f\u3002\u5f53\u521d\u306e\u4e88\u5b9a\u306f\u5217\u8eca\u306e\u9045\u5ef6\u3067\u5927\u5e45\u306b\u72c2\u3044\u3001\u6642\u9593\u3060\u3051\u304c\u305f\u3060\u6b8b\u9177\u306b\u6d41\u308c\u3066\u3044\u304f\u2026\u3002\u8cb4\u6a39\u3068\u660e\u91cc\u306e\u3001\u518d\u4f1a\u3068\u5225\u308c\u306e1\u65e5\u3092\u6642\u9593\u7d4c\u904e\u3068\u5171\u306b\u63cf\u304f\u3002"], "additional_info": {"answer": "\u6771\u4eac\u306e\u5c0f\u5b66\u751f\u30fb\u9060\u91ce\u8cb4\u6a39\u3068\u7be0\u539f\u660e\u91cc\u306f\u304a\u4e92\u3044\u306b\u5bfe\u3059\u308b\u300c\u4ed6\u4eba\u306b\u306f\u5206\u3089\u306a\u3044\u7279\u5225\u306a\u60f3\u3044\u300d\u3092\u62b1\u3048\u3066\u3044\u305f\u3002\u3057\u304b\u3057\u5c0f\u5b66\u6821\u5352\u696d\u3068\u540c\u6642\u306b\u660e\u91cc\u306f\u6803\u6728\u3078\u8ee2\u6821\u3057\u3066\u3057\u307e\u3044\u3001\u305d\u308c\u304d\u308a\u4f1a\u3046\u3053\u3068\u304c\u7121\u304f\u306a\u3063\u3066\u3057\u307e\u3046\u3002\u8cb4\u6a39\u304c\u4e2d\u5b66\u306b\u5165\u5b66\u3057\u3066\u534a\u5e74\u304c\u7d4c\u904e\u3057\u305f\u590f\u306e\u3042\u308b\u65e5\u3001\u6803\u6728\u306e\u660e\u91cc\u304b\u3089\u624b\u7d19\u304c\u5c4a\u304f\u3002\u305d\u308c\u3092\u304d\u3063\u304b\u3051\u306b\u3001\u6587\u901a\u3092\u91cd\u306d\u308b\u3088\u3046\u306b\u306a\u308b2\u4eba\u3002\u3057\u304b\u3057\u305d\u306e\u5e74\u306e\u51ac\u306b\u3001\u4eca\u5ea6\u306f\u8cb4\u6a39\u304c\u9e7f\u5150\u5cf6\u3078\u8ee2\u6821\u3059\u308b\u3053\u3068\u304c\u6c7a\u307e\u3063\u305f\u3002\u9e7f\u5150\u5cf6\u3068\u6803\u6728\u3067\u306f\u7d76\u671b\u7684\u306b\u9060\u3044\u3002\u300c\u3082\u3046\u4e8c\u5ea6\u3068\u4f1a\u3048\u306a\u304f\u306a\u308b\u304b\u3082\u3057\u308c\u306a\u3044\u2026\u300d\u305d\u3046\u601d\u3063\u305f\u8cb4\u6a39\u306f\u3001\u660e\u91cc\u306b\u4f1a\u3044\u306b\u884c\u304f\u6c7a\u610f\u3092\u3059\u308b\u3002\u3057\u304b\u3057\u305d\u306e\u7d04\u675f\u306e\u65e5\u3001\u95a2\u6771\u3067\u306f\u5927\u96ea\u3068\u306a\u3063\u305f\u3002\u5f53\u521d\u306e\u4e88\u5b9a\u306f\u5217\u8eca\u306e\u9045\u5ef6\u3067\u5927\u5e45\u306b\u72c2\u3044\u3001\u6642\u9593\u3060\u3051\u304c\u305f\u3060\u6b8b\u9177\u306b\u6d41\u308c\u3066\u3044\u304f\u2026\u3002\u8cb4\u6a39\u3068\u660e\u91cc\u306e\u3001\u518d\u4f1a\u3068\u5225\u308c\u306e1\u65e5\u3092\u6642\u9593\u7d4c\u904e\u3068\u5171\u306b\u63cf\u304f\u3002", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 177, "goal": "May I request the Mandarin translation of the overview for the film \"A Silent Voice: The Movie\"?", "subgoals": ["\u300a\u58f0\u4e4b\u5f62\u300b\u662f\u7ee7\u300a\u4f60\u7684\u540d\u5b57\u3002\u300b\u4e4b\u540e\uff0c\u65e5\u672c\u53c8\u4e00\u90e8\u73b0\u8c61\u7ea7\u7684\u52a8\u6f2b\u7535\u5f71\u3002\u8bb2\u8ff0\u5584\u826f\u3001\u53ef\u7231\uff0c\u53c8\u6709\u542c\u89c9\u969c\u788d\u7684\u5c11\u5973\u897f\u5bab\u785d\u5b50\uff08\u65e9\u89c1\u6c99\u7ec7 \u914d\u97f3\uff09\uff0c\u548c\u66fe\u7ecf\u4f24\u5bb3\u8fc7\u5979\u7684\u5c11\u5e74\u77f3\u7530\u5c06\u4e5f\uff08\u5165\u91ce\u81ea\u7531 \u914d\u97f3\uff09\u7684\u6545\u4e8b\u3002\u897f\u5bab\u785d\u5b50\u5728\u5b66\u6821\u5907\u53d7\u6b3a\u8d1f\uff0c\u5f97\u4e0d\u5230\u53cb\u60c5\u548c\u5173\u7231\uff0c\u4f46\u5979\u603b\u662f\u5bf9\u5468\u56f4\u4e00\u5207\u62b1\u4ee5\u5584\u610f\u3002\u77f3\u7530\u5c06\u4e5f\u662f\u4e3b\u8981\u65bd\u66b4\u8005\u4e4b\u4e00\uff0c\u4ed6\u56e0\u6b64\u4e5f\u88ab\u5468\u56f4\u7684\u540c\u5b66\u5b64\u7acb\u8d77\u6765\u3002\u7ecf\u8fc7\u4e94\u5e74\u65f6\u95f4\uff0c\u4e24\u4eba\u6b65\u5165\u9ad8\u4e2d\uff0c\u968f\u7740\u6210\u957f\u4ed6\u4eec\u9010\u6e10\u4f53\u4f1a\u5230\u5f7c\u6b64\u7684\u5fc3\u60c5\u548c\u5904\u5883\u3002\u5728\u8dcc\u8dcc\u649e\u649e\u7684\u9752\u6625\u4e2d\uff0c\u5c11\u7537\u5c11\u5973\u4eec\u7ec8\u4e8e\u5b66\u4f1a\u4e86\u63a5\u7eb3\u522b\u4eba\uff0c\u4e0e\u81ea\u5df1\u548c\u89e3\u3002"], "additional_info": {"answer": "\u300a\u58f0\u4e4b\u5f62\u300b\u662f\u7ee7\u300a\u4f60\u7684\u540d\u5b57\u3002\u300b\u4e4b\u540e\uff0c\u65e5\u672c\u53c8\u4e00\u90e8\u73b0\u8c61\u7ea7\u7684\u52a8\u6f2b\u7535\u5f71\u3002\u8bb2\u8ff0\u5584\u826f\u3001\u53ef\u7231\uff0c\u53c8\u6709\u542c\u89c9\u969c\u788d\u7684\u5c11\u5973\u897f\u5bab\u785d\u5b50\uff08\u65e9\u89c1\u6c99\u7ec7 \u914d\u97f3\uff09\uff0c\u548c\u66fe\u7ecf\u4f24\u5bb3\u8fc7\u5979\u7684\u5c11\u5e74\u77f3\u7530\u5c06\u4e5f\uff08\u5165\u91ce\u81ea\u7531 \u914d\u97f3\uff09\u7684\u6545\u4e8b\u3002\u897f\u5bab\u785d\u5b50\u5728\u5b66\u6821\u5907\u53d7\u6b3a\u8d1f\uff0c\u5f97\u4e0d\u5230\u53cb\u60c5\u548c\u5173\u7231\uff0c\u4f46\u5979\u603b\u662f\u5bf9\u5468\u56f4\u4e00\u5207\u62b1\u4ee5\u5584\u610f\u3002\u77f3\u7530\u5c06\u4e5f\u662f\u4e3b\u8981\u65bd\u66b4\u8005\u4e4b\u4e00\uff0c\u4ed6\u56e0\u6b64\u4e5f\u88ab\u5468\u56f4\u7684\u540c\u5b66\u5b64\u7acb\u8d77\u6765\u3002\u7ecf\u8fc7\u4e94\u5e74\u65f6\u95f4\uff0c\u4e24\u4eba\u6b65\u5165\u9ad8\u4e2d\uff0c\u968f\u7740\u6210\u957f\u4ed6\u4eec\u9010\u6e10\u4f53\u4f1a\u5230\u5f7c\u6b64\u7684\u5fc3\u60c5\u548c\u5904\u5883\u3002\u5728\u8dcc\u8dcc\u649e\u649e\u7684\u9752\u6625\u4e2d\uff0c\u5c11\u7537\u5c11\u5973\u4eec\u7ec8\u4e8e\u5b66\u4f1a\u4e86\u63a5\u7eb3\u522b\u4eba\uff0c\u4e0e\u81ea\u5df1\u548c\u89e3\u3002", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 178, "goal": "Could you provide the Russian translation of the overview for the film \"About Love\"?", "subgoals": ["\u041a\u0440\u0430\u0441\u0430\u0432\u0438\u0446\u0430 \u041d\u0438\u043d\u0430 \u0436\u0438\u0432\u0435\u0442, \u043a\u0430\u043a \u0435\u0439 \u00a0\u043a\u0430\u0436\u0435\u0442\u0441\u044f, \u0432 \u00a0\u0441\u0447\u0430\u0441\u0442\u043b\u0438\u0432\u043e\u043c \u0431\u0440\u0430\u043a\u0435 \u0441 \u00a0\u0438\u043d\u0442\u0435\u043b\u043b\u0438\u0433\u0435\u043d\u0442\u043d\u044b\u043c \u043f\u0440\u043e\u0444\u0435\u0441\u0441\u043e\u0440\u043e\u043c-\u043a\u0438\u0442\u0430\u0438\u0441\u0442\u043e\u043c \u0410\u043b\u0435\u043a\u0441\u0430\u043d\u0434\u0440\u043e\u043c, \u043f\u043e \u00a0\u0441\u043e\u0432\u043c\u0435\u0441\u0442\u0438\u0442\u0435\u043b\u044c\u0441\u0442\u0432\u0443 \u0435\u0435 \u00a0\u043f\u0440\u0435\u043f\u043e\u0434\u0430\u0432\u0430\u0442\u0435\u043b\u0435\u043c. \u041d\u043e \u00a0\u0434\u043e\u043b\u0433 \u0437\u0430 \u00a0\u0438\u043f\u043e\u0442\u0435\u043a\u0443 \u043f\u043e\u0434\u0441\u043f\u0443\u0434\u043d\u043e \u0434\u0430\u0432\u0438\u0442 \u043d\u0430 \u00a0\u0441\u0443\u043f\u0440\u0443\u0436\u0435\u0441\u043a\u0438\u0435 \u043e\u0442\u043d\u043e\u0448\u0435\u043d\u0438\u044f. \u041e\u0434\u043d\u0430\u0436\u0434\u044b \u041d\u0438\u043d\u0430 \u0437\u043d\u0430\u043a\u043e\u043c\u0438\u0442\u0441\u044f \u0441 \u00a0\u0421\u0435\u0440\u0433\u0435\u0435\u043c, \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u0435\u043c \u0442\u043e\u0433\u043e \u0441\u0430\u043c\u043e\u0433\u043e \u0431\u0430\u043d\u043a\u0430, \u043a\u043e\u0442\u043e\u0440\u043e\u043c\u0443 \u0434\u043e\u043b\u0436\u0435\u043d \u0435\u0435 \u00a0\u043c\u0443\u0436, \u0438 \u00a0\u0434\u0435\u043b\u043e\u0432\u0430\u044f \u0432\u0441\u0442\u0440\u0435\u0447\u0430 \u043f\u0440\u0435\u0432\u0440\u0430\u0449\u0430\u0435\u0442\u0441\u044f \u0432 \u00a0\u043d\u0435\u0444\u043e\u0440\u043c\u0430\u043b\u044c\u043d\u0443\u044e. \u0415\u0434\u0432\u0430 \u00a0\u043b\u0438 \u041d\u0438\u043d\u0443 \u0432\u0435\u0434\u0435\u0442 \u043a \u00a0\u044d\u0444\u0444\u0435\u043a\u0442\u043d\u043e\u043c\u0443 \u043c\u0443\u0436\u0447\u0438\u043d\u0435 \u0441\u0438\u0442\u0443\u0430\u0446\u0438\u044f \u0441 \u00a0\u0438\u043f\u043e\u0442\u0435\u043a\u043e\u0439, \u043a\u043e\u0442\u043e\u0440\u0430\u044f \u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u0441\u044f \u043b\u0438\u0448\u044c \u043f\u043e\u0432\u043e\u0434\u043e\u043c \u043e\u0442\u0434\u0430\u0442\u044c\u0441\u044f \u0432\u043b\u0435\u0447\u0435\u043d\u0438\u044e. \u0420\u043e\u043c\u0430\u043d \u0441 \u00a0\u0436\u0435\u043d\u0430\u0442\u044b\u043c \u0421\u0435\u0440\u0433\u0435\u0435\u043c \u0432\u0440\u044f\u0434 \u00a0\u043b\u0438 \u0441\u0443\u043b\u0438\u0442 \u0441\u0447\u0430\u0441\u0442\u044c\u0435, \u043d\u043e \u00a0\u0434\u0435\u0432\u0443\u0448\u043a\u0430 \u043e\u0441\u043e\u0437\u043d\u0430\u0435\u0442, \u0447\u0442\u043e \u0432\u043f\u0435\u0440\u0432\u044b\u0435 \u0447\u0443\u0432\u0441\u0442\u0432\u0443\u0435\u0442 \u043d\u0430\u0441\u0442\u043e\u044f\u0449\u0443\u044e \u043b\u044e\u0431\u043e\u0432\u044c."], "additional_info": {"answer": "\u041a\u0440\u0430\u0441\u0430\u0432\u0438\u0446\u0430 \u041d\u0438\u043d\u0430 \u0436\u0438\u0432\u0435\u0442, \u043a\u0430\u043a \u0435\u0439 \u00a0\u043a\u0430\u0436\u0435\u0442\u0441\u044f, \u0432 \u00a0\u0441\u0447\u0430\u0441\u0442\u043b\u0438\u0432\u043e\u043c \u0431\u0440\u0430\u043a\u0435 \u0441 \u00a0\u0438\u043d\u0442\u0435\u043b\u043b\u0438\u0433\u0435\u043d\u0442\u043d\u044b\u043c \u043f\u0440\u043e\u0444\u0435\u0441\u0441\u043e\u0440\u043e\u043c-\u043a\u0438\u0442\u0430\u0438\u0441\u0442\u043e\u043c \u0410\u043b\u0435\u043a\u0441\u0430\u043d\u0434\u0440\u043e\u043c, \u043f\u043e \u00a0\u0441\u043e\u0432\u043c\u0435\u0441\u0442\u0438\u0442\u0435\u043b\u044c\u0441\u0442\u0432\u0443 \u0435\u0435 \u00a0\u043f\u0440\u0435\u043f\u043e\u0434\u0430\u0432\u0430\u0442\u0435\u043b\u0435\u043c. \u041d\u043e \u00a0\u0434\u043e\u043b\u0433 \u0437\u0430 \u00a0\u0438\u043f\u043e\u0442\u0435\u043a\u0443 \u043f\u043e\u0434\u0441\u043f\u0443\u0434\u043d\u043e \u0434\u0430\u0432\u0438\u0442 \u043d\u0430 \u00a0\u0441\u0443\u043f\u0440\u0443\u0436\u0435\u0441\u043a\u0438\u0435 \u043e\u0442\u043d\u043e\u0448\u0435\u043d\u0438\u044f. \u041e\u0434\u043d\u0430\u0436\u0434\u044b \u041d\u0438\u043d\u0430 \u0437\u043d\u0430\u043a\u043e\u043c\u0438\u0442\u0441\u044f \u0441 \u00a0\u0421\u0435\u0440\u0433\u0435\u0435\u043c, \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u0435\u043c \u0442\u043e\u0433\u043e \u0441\u0430\u043c\u043e\u0433\u043e \u0431\u0430\u043d\u043a\u0430, \u043a\u043e\u0442\u043e\u0440\u043e\u043c\u0443 \u0434\u043e\u043b\u0436\u0435\u043d \u0435\u0435 \u00a0\u043c\u0443\u0436, \u0438 \u00a0\u0434\u0435\u043b\u043e\u0432\u0430\u044f \u0432\u0441\u0442\u0440\u0435\u0447\u0430 \u043f\u0440\u0435\u0432\u0440\u0430\u0449\u0430\u0435\u0442\u0441\u044f \u0432 \u00a0\u043d\u0435\u0444\u043e\u0440\u043c\u0430\u043b\u044c\u043d\u0443\u044e. \u0415\u0434\u0432\u0430 \u00a0\u043b\u0438 \u041d\u0438\u043d\u0443 \u0432\u0435\u0434\u0435\u0442 \u043a \u00a0\u044d\u0444\u0444\u0435\u043a\u0442\u043d\u043e\u043c\u0443 \u043c\u0443\u0436\u0447\u0438\u043d\u0435 \u0441\u0438\u0442\u0443\u0430\u0446\u0438\u044f \u0441 \u00a0\u0438\u043f\u043e\u0442\u0435\u043a\u043e\u0439, \u043a\u043e\u0442\u043e\u0440\u0430\u044f \u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u0441\u044f \u043b\u0438\u0448\u044c \u043f\u043e\u0432\u043e\u0434\u043e\u043c \u043e\u0442\u0434\u0430\u0442\u044c\u0441\u044f \u0432\u043b\u0435\u0447\u0435\u043d\u0438\u044e. \u0420\u043e\u043c\u0430\u043d \u0441 \u00a0\u0436\u0435\u043d\u0430\u0442\u044b\u043c \u0421\u0435\u0440\u0433\u0435\u0435\u043c \u0432\u0440\u044f\u0434 \u00a0\u043b\u0438 \u0441\u0443\u043b\u0438\u0442 \u0441\u0447\u0430\u0441\u0442\u044c\u0435, \u043d\u043e \u00a0\u0434\u0435\u0432\u0443\u0448\u043a\u0430 \u043e\u0441\u043e\u0437\u043d\u0430\u0435\u0442, \u0447\u0442\u043e \u0432\u043f\u0435\u0440\u0432\u044b\u0435 \u0447\u0443\u0432\u0441\u0442\u0432\u0443\u0435\u0442 \u043d\u0430\u0441\u0442\u043e\u044f\u0449\u0443\u044e \u043b\u044e\u0431\u043e\u0432\u044c.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 179, "goal": "What is the IMDB ID of the actor who portrayed Batman in \"The Dark Knight\" trilogy directed by Christopher Nolan? Please provide the IMDB ID as a string.", "subgoals": ["nm0000288"], "additional_info": {"answer": "nm0000288", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 180, "goal": "May I request the Twitter ID of the actor known for his role as Jack Dawson in \"Titanic\"? Please provide the Twitter ID as a string.", "subgoals": ["leodicaprio"], "additional_info": {"answer": "leodicaprio", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 181, "goal": "What is the Facebook ID of the actress who starred in \"Gone with the Wind\"? Please answer me with the actress name.", "subgoals": ["Vivien Leigh"], "additional_info": {"answer": "Vivien Leigh", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 182, "goal": "Could you provide the Twitter ID of the actor known for playing Neo in \"The Matrix\" trilogy? Please answer me with the actor's name.", "subgoals": ["Keanu Reeves"], "additional_info": {"answer": "Keanu Reeves", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 183, "goal": "May I ask for the Instagram ID of the actress who portrayed Rey in the Star Wars sequel trilogy? Please provide the Instagram ID as a string.", "subgoals": ["daisyridley"], "additional_info": {"answer": "daisyridley", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 184, "goal": "What is the Facebook ID of the actor who played Spider-Man in the Marvel Cinematic Universe? Please answer me with the actor's name.", "subgoals": ["Tom Holland"], "additional_info": {"answer": "Tom Holland", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 185, "goal": "Could you give me the Twitter ID of the actress known for her role as Princess Leia in the original \"Star Wars\" trilogy? Please provide the Twitter ID as a string.", "subgoals": ["carrieffisher"], "additional_info": {"answer": "carrieffisher", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 186, "goal": "May I request the IMDB ID of the actor known for portraying Captain Jack Sparrow in the \"Pirates of the Caribbean\" franchise? Please provide the answer as a string.", "subgoals": ["nm0000136"], "additional_info": {"answer": "nm0000136", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 187, "goal": "Can you provide the production companies for both \"The Matrix\" and \"Inception\"? Are there any similarities? Please answer me with the production company name as a string.", "subgoals": ["Warner Bros. Pictures"], "additional_info": {"answer": "Warner Bros. Pictures", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 188, "goal": "Which film had a bigger impact on the box office, \"The Avengers\" or \"Avengers: Age of Ultron\"? Please answer me with the film name as a string.", "subgoals": ["The Avengers"], "additional_info": {"answer": "The Avengers", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 189, "goal": "Among \"The Social Network\" and \"The Big Short,\" which movie has a larger budget? Please answer me with the film name as a string.", "subgoals": ["The Social Network"], "additional_info": {"answer": "The Social Network", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 190, "goal": "Compare the genres of \"The Silence of the Lambs\" and \"Se7en.\" Do they share any common genres? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 191, "goal": "Between \"Titanic\" and \"Avatar,\" which movie has more production countries involved? Please answer me with the film name as a string.", "subgoals": ["Avatar"], "additional_info": {"answer": "Avatar", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 192, "goal": "Which film, \"Inglourious Basterds\" or \"Django Unchained,\" has more keywords associated with it? Please answer me with the film name that has more keywords as a string.", "subgoals": ["Django Unchained"], "additional_info": {"answer": "Django Unchained", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 193, "goal": "When it comes to revenue, which movie performed better, \"The Lion King\" (1994) or \"Beauty and the Beast\" (1991)? Please answer me with the film name as a string.", "subgoals": ["The Lion King"], "additional_info": {"answer": "The Lion King", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 194, "goal": "Among \"The Dark Knight\" and \"The Dark Knight Rises,\" which movie has a higher average vote score? Please answer me with the film name as a string.", "subgoals": ["The Dark Knight"], "additional_info": {"answer": "The Dark Knight", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 195, "goal": "How do the budgets of \"Avengers: Infinity War\" and \"Avengers: Endgame\" compare to each other? Please answer me with the movie name and budget as a string.", "subgoals": ["'Avengers: Endgame' ($356,000,000) is higher than 'Avengers: Infinity War' ($300,000,000)"], "additional_info": {"answer": "'Avengers: Endgame' ($356,000,000) is higher than 'Avengers: Infinity War' ($300,000,000)", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 196, "goal": "Compare the release dates of Star Wars: Episode IV - A New Hope and Star Wars: Episode V - The Empire Strikes Back. Which one was released first? Please answer me with the film name as a string.", "subgoals": ["Star Wars: Episode IV - A New Hope"], "additional_info": {"answer": "Star Wars: Episode IV - A New Hope", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 197, "goal": "The actor who played Neo in \"The Matrix\", what role did he play in \"John Wick\"? Please answer me with the character name as a string.", "subgoals": ["John Wick"], "additional_info": {"answer": "John Wick", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 198, "goal": "The actress who played Rey in \"Star Wars: The Rise of Skywalker\", what role did she play in \"Murder on the Orient Express\"? Please answer me with the character name as a string.", "subgoals": ["Mary Debenham"], "additional_info": {"answer": "Mary Debenham", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 199, "goal": "The actor who played Captain Jack Sparrow in \"Pirates of the Caribbean: The Curse of the Black Pearl\", what role did he play in \"Edward Scissorhands\"? Please answer me with the character name as a string.", "subgoals": ["Edward Scissorhands"], "additional_info": {"answer": "Edward Scissorhands", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 200, "goal": "The actress who played Katniss Everdeen in \"The Hunger Games: Mockingjay - Part 2\", what role did she play in \"Silver Linings Playbook\"? Please answer me with the character name as a string.", "subgoals": ["Tiffany Maxwell"], "additional_info": {"answer": "Tiffany Maxwell", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 201, "goal": "The actor who played Forrest Gump in \"Forrest Gump\", what role did he play in \"Cast Away\"? Please answer me with the character name as a string.", "subgoals": ["Chuck Noland"], "additional_info": {"answer": "Chuck Noland", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 202, "goal": "The actor who played Bruce Wayne in \"The Dark Knight\", what role did he play in \"Inception\"? Please answer me with the character name as a string.", "subgoals": ["Christian Bale did not have a role in 'Inception'."], "additional_info": {"answer": "Christian Bale did not have a role in 'Inception'.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 203, "goal": "Can you provide me with the biography of the actress who played Rey in \"Star Wars: The Force Awakens\"? Please answer me with a brief description.", "subgoals": ["Daisy Jazz Isobel Ridley (born 10 April 1992) is an English actress, who rose to prominence for her role as Rey in the Star Wars sequel trilogy: The Force Awakens (2015), The Last Jedi (2017), and The Rise of Skywalker (2019).\n\nShe also appeared in the mystery film Murder on the Orient Express (2017), played the title character of the romantic drama Ophelia (2018), and has done occasional voice acting, notably the live-action/animated film Peter Rabbit (2018) and video games such as 12 Minutes.\n\nDescription above from the Wikipedia article Daisy Ridley, licensed under CC-BY-SA, full list of contributors on Wikipedia."], "additional_info": {"answer": "Daisy Jazz Isobel Ridley (born 10 April 1992) is an English actress, who rose to prominence for her role as Rey in the Star Wars sequel trilogy: The Force Awakens (2015), The Last Jedi (2017), and The Rise of Skywalker (2019).\n\nShe also appeared in the mystery film Murder on the Orient Express (2017), played the title character of the romantic drama Ophelia (2018), and has done occasional voice acting, notably the live-action/animated film Peter Rabbit (2018) and video games such as 12 Minutes.\n\nDescription above from the Wikipedia article Daisy Ridley, licensed under CC-BY-SA, full list of contributors on Wikipedia.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 204, "goal": "Could you find out if the actor who played Tony Stark in \"Iron Man\" also appeared in \"The Judge\"? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 205, "goal": "Among the movies directed by Quentin Tarantino, which one has the highest budget? Please answer me with the film name as a string.", "subgoals": ["Kill Bill: Vol. 1"], "additional_info": {"answer": "Kill Bill: Vol. 1", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 206, "goal": "Is there any movie where both Leonardo DiCaprio and Kate Winslet starred together after \"Titanic\"? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 207, "goal": "Can you find out if the actress who portrayed Hermione Granger in the \"Harry Potter\" series appeared in any movies directed by Steven Spielberg? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 208, "goal": "What is the average vote score of the movies directed by Martin Scorsese? Please provide the answer as a number.", "subgoals": [7.64], "additional_info": {"answer": 7.64, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 209, "goal": "Is there any movie where both Tom Hanks and Meg Ryan starred together? Please answer me with the film name as a string.", "subgoals": ["Sleepless in Seattle"], "additional_info": {"answer": "Sleepless in Seattle", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 210, "goal": "Is there any movie where both Brad Pitt and Angelina Jolie starred together? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 211, "goal": "What is the birthday of the actor who played Jack Dawson in \"Titanic\"? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1974-11-11"], "additional_info": {"answer": "1974-11-11", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 212, "goal": "Can you find out if the actor who portrayed Jon Snow in \"Game of Thrones\" appeared in any movies directed by Ridley Scott? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 213, "goal": "What is the biography of the actor who played Spider-Man in Spider-Man: Homecoming? Please provide the information as a string.", "subgoals": ["Thomas \"Tom\" Stanley Holland is an English actor and dancer.\n\nHe is best known for playing Peter Parker / Spider-Man in the Marvel Cinematic Universe and has appeared as the character in six films: Captain America: Civil War (2016), Spider-Man: Homecoming (2017), Avengers: Infinity War (2018), Avengers: Endgame (2019), Spider-Man: Far From Home (2019), and Spider-Man: No Way Home (2021).\n\nHe is also known for playing the title role in Billy Elliot the Musical at the Victoria Palace Theatre, London, as well as for starring in the 2012 film The Impossible."], "additional_info": {"answer": "Thomas \"Tom\" Stanley Holland is an English actor and dancer.\n\nHe is best known for playing Peter Parker / Spider-Man in the Marvel Cinematic Universe and has appeared as the character in six films: Captain America: Civil War (2016), Spider-Man: Homecoming (2017), Avengers: Infinity War (2018), Avengers: Endgame (2019), Spider-Man: Far From Home (2019), and Spider-Man: No Way Home (2021).\n\nHe is also known for playing the title role in Billy Elliot the Musical at the Victoria Palace Theatre, London, as well as for starring in the 2012 film The Impossible.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 214, "goal": "Could you provide the revenue of the movie in which Vin Diesel portrayed Dominic Toretto? Please give me a number.", "subgoals": [207283925], "additional_info": {"answer": 207283925, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 215, "goal": "Is there any movie where both Emma Stone and Ryan Gosling starred together after \"La La Land\"? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 216, "goal": "What is the place of birth of the actress who played Katniss Everdeen in \"The Hunger Games\"? Please provide the birthplace as a string.", "subgoals": ["Indian Hills, Kentucky, USA"], "additional_info": {"answer": "Indian Hills, Kentucky, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 217, "goal": "Among the movies directed by Ridley Scott, which one has the highest number of production countries? Please answer me with the film names as an array.", "subgoals": [["1492: Conquest of Paradise", "Kingdom of Heaven"]], "additional_info": {"answer": ["1492: Conquest of Paradise", "Kingdom of Heaven"], "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 218, "goal": "Can you find out if the actor who played Aragorn in \"The Lord of the Rings\" series appeared in any movies directed by Peter Jackson? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 219, "goal": "Could you provide the budget of the movie in which Daniel Craig portrayed James Bond? Please give me a number.", "subgoals": [*********], "additional_info": {"answer": *********, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 220, "goal": "Is there any movie where both Emma Watson and Rupert Grint starred together after the \"Harry Potter\" series? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 221, "goal": "What is the birthday of the actor who played Bruce Wayne in \"The Dark Knight\" trilogy? Please provide the date in the format of YYYY-MM-DD.", "subgoals": ["1974-01-30"], "additional_info": {"answer": "1974-01-30", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 222, "goal": "Among the movies directed by Peter Jackson, which one has the highest revenue? Please answer me with the film name as a string.", "subgoals": ["The Lord of the Rings: The Return of the King"], "additional_info": {"answer": "The Lord of the Rings: The Return of the King", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 223, "goal": "Can you find out if the actress who portrayed Black Widow in the Marvel Cinematic Universe appeared in any movies directed by the Russo brothers? Please answer me with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 224, "goal": "What is the biography of the actor who played Thor in the Marvel Cinematic Universe?", "subgoals": ["Chris Hemsworth (born 11 August 1983) is an Australian actor. He is best known for his roles as Kim Hyde in the Australian TV series Home and Away (2004) and as Thor in the Marvel Cinematic Universe films Thor (2011), The Avengers (2012), Thor: The Dark World (2013), Avengers: Age of Ultron (2015), Thor: Ragnarok (2017), Avengers: Infinity War (2018), Avengers: Endgame (2019), and Thor: Love and Thunder (2022). He has also appeared in the science fiction action film Star Trek (2009), the thriller adventure A Perfect Getaway (2009), the horror comedy The Cabin in the Woods (2012), the dark fantasy action film Snow White and the Huntsman (2012), the war film Red Dawn (2012) and the biographical sports drama film Rush (2013).\n\nDescription above from the Wikipedia article Chris Hemsworth, licensed under CC-BY-SA, full list of contributors on Wikipedia."], "additional_info": {"answer": "Chris Hemsworth (born 11 August 1983) is an Australian actor. He is best known for his roles as Kim Hyde in the Australian TV series Home and Away (2004) and as Thor in the Marvel Cinematic Universe films Thor (2011), The Avengers (2012), Thor: The Dark World (2013), Avengers: Age of Ultron (2015), Thor: Ragnarok (2017), Avengers: Infinity War (2018), Avengers: Endgame (2019), and Thor: Love and Thunder (2022). He has also appeared in the science fiction action film Star Trek (2009), the thriller adventure A Perfect Getaway (2009), the horror comedy The Cabin in the Woods (2012), the dark fantasy action film Snow White and the Huntsman (2012), the war film Red Dawn (2012) and the biographical sports drama film Rush (2013).\n\nDescription above from the Wikipedia article Chris Hemsworth, licensed under CC-BY-SA, full list of contributors on Wikipedia.", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 225, "goal": "Could you provide the revenue of the movie in which Scarlett Johansson portrayed Natasha Romanoff? Please give me a number.", "subgoals": [*********], "additional_info": {"answer": *********, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 226, "goal": "Is there any movie where both Chris Evans and Robert Downey Jr. starred together? Please answer me with the film name as a string.", "subgoals": ["The Avengers"], "additional_info": {"answer": "The Avengers", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 227, "goal": "What is the place of birth of the actor who played Iron Man in the Marvel Cinematic Universe? Please answer me with the birthplace as a string.", "subgoals": ["New York City, New York, USA"], "additional_info": {"answer": "New York City, New York, USA", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 228, "goal": "Can you find out if the actress who portrayed Queen Elizabeth II in \"The Crown\" appeared in any movies directed by Stephen Daldry? Please answer with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 229, "goal": "What is the release date of the movie in which Meryl Streep portrayed Margaret Thatcher?Please provide the date in the format YYYY-MM-DD.", "subgoals": ["2011-12-26"], "additional_info": {"answer": "2011-12-26", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 230, "goal": "Among the movies directed by Clint Eastwood, which one has the highest vote average? Please answer me with the film name as a string.", "subgoals": ["Million Dollar Baby"], "additional_info": {"answer": "Million Dollar Baby", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 231, "goal": "Can you provide the budget of the movie in which Sandra Bullock portrayed Leigh Anne Tuohy? Please give me a number.", "subgoals": [29000000], "additional_info": {"answer": 29000000, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 232, "goal": "Is there any movie where both Tom Hardy and Charlize Theron starred together after \"Mad Max: Fury Road\"? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 233, "goal": "What is the average vote score of the movies in which Denzel Washington portrayed Malcolm X? Please provide the average vote score as a number.", "subgoals": [7.531], "additional_info": {"answer": 7.531, "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 234, "goal": "Could you find out if the actor who played Neo in \"The Matrix\" appeared in any movies directed by Lana Wachowski? Please answer with Yes or No.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 235, "goal": "Among the movies directed by David Fincher, which one has the highest revenue? Please answer me with the film name as a string.", "subgoals": ["The Curious Case of Benjamin Button"], "additional_info": {"answer": "The Curious Case of Benjamin Button", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 236, "goal": "Can you provide the release date of the movie in which Brad Pitt portrayed Tyler Durden? Please provide the date in the format YYYY-MM-DD.", "subgoals": ["1999-10-15"], "additional_info": {"answer": "1999-10-15", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
{"id": 237, "goal": "Is there any movie where both Natalie Portman and Mila Kunis starred together after \"Black Swan\"? Please answer me with Yes or No.", "subgoals": ["No"], "additional_info": {"answer": "No", "init_config": null, "goal_type": 0, "tool": "movie"}, "difficulty": "hard"}
