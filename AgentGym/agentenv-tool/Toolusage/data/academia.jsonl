{"task": "tool-query", "id": 0, "goal": "How many mutual collaborators do <PERSON><PERSON><PERSON> and <PERSON><PERSON> share? Please give me a numerical value as an answer.", "subgoals": [2], "additional_info": {"answer": 2, "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 1, "goal": "Which paper by <PERSON><PERSON><PERSON> has the most citations in the DBLP citation network?", "subgoals": ["Efficient Graph Convolution for Joint Node Representation Learning and Clustering"], "additional_info": {"answer": "Efficient Graph Convolution for Joint Node Representation Learning and Clustering", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 2, "goal": "Who are the co-authors with whom <PERSON><PERSON><PERSON> has written the paper that has the highest number of citations in the DBLP citation network? Please answer in the form of a list ['author1', 'author2', ...].", "subgoals": [["<PERSON><PERSON><PERSON>", "<PERSON>"]], "additional_info": {"answer": ["Chakib Fettal", "Mohamed Nadif"], "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 3, "goal": "In the DBLP citation network, which venue has the most publications by Andrea Omicini?", "subgoals": ["EXPLAINABLE AND TRANSPARENT AI AND MULTI-AGENT SYSTEMS, EXTRAAMAS 2022"], "additional_info": {"answer": "EXPLAINABLE AND TRANSPARENT AI AND MULTI-AGENT SYSTEMS, EXTRAAMAS 2022", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 4, "goal": "How many citations do papers co-authored by Florian Kirchbuchner and Fadi Boutros have in the DBLP citation network? Please give me a numerical value as an answer.", "subgoals": [51], "additional_info": {"answer": 51, "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 5, "goal": "Who has the most collaborations with Lazhar Labiod in the DBLP citation network?", "subgoals": ["Mohamed Nadif"], "additional_info": {"answer": "Mohamed Nadif", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 6, "goal": "Which organizations were involved in the research titled 'mToFNet: Object Anti-Spoofing with Mobile Time-of-Flight Data'? Please answer in the form of a list ['org1', 'org2', ...]", "subgoals": [["Chung-Ang University", "Line+"]], "additional_info": {"answer": ["Line+", "Chung-Ang University"], "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 7, "goal": "Which keyword does Correia Miguel emphasize most frequently in the DBLP citation network?", "subgoals": ["Intel SGX"], "additional_info": {"answer": "Intel SGX", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 8, "goal": "Who wrote the paper titled 'Density Ratio Estimation via Infinitesimal Classification'? Please answer in the form of a list ['author1', 'author2', ...].", "subgoals": [["Chenlin Meng", "Stefano Ermon", "Yang Song_2"]], "additional_info": {"answer": ["Chenlin Meng", "Yang Song_2", "Stefano Ermon"], "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 9, "goal": "Do the authors of 'Decoupled Dynamic Spatial-Temporal Graph Neural Network for Traffic Forecasting._2' and 'Evolutionary Clustering of Moving Objects' have any overlap? Please respond with 'Yes' or 'No'.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 10, "goal": "Which organizations is Yilun Zhou affiliated with?", "subgoals": ["MIT"], "additional_info": {"answer": "MIT", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 11, "goal": "Is Tonghan Wang_2 affiliated with the same organization as Zeyang Zhang? Please answer 'Yes' or 'No'.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 12, "goal": "How many papers within the DBLP citation network reference the work titled 'Joint extraction of entities and overlapping relations by improved graph convolutional networks'? Please give me a numerical value as an answer.", "subgoals": [8], "additional_info": {"answer": 8, "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 13, "goal": "Which paper has received more citations: 'Stability and Risk Bounds of Iterative Hard Thresholding' or 'Compressive Wideband Spectrum Sensing and Signal Recovery With Unknown Multipath Channels'?", "subgoals": ["Stability and Risk Bounds of Iterative Hard Thresholding"], "additional_info": {"answer": "Stability and Risk Bounds of Iterative Hard Thresholding", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 14, "goal": "Which paper was published first: 'Topology optimization of structures undergoing brittle fracture' or 'Classification Model for IDS Using Auto Cryptographic Denoising Technique'?", "subgoals": ["Topology optimization of structures undergoing brittle fracture"], "additional_info": {"answer": "Topology optimization of structures undergoing brittle fracture", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 15, "goal": "Is the article 'Road Traffic Forecast Based on Meteorological Information through Deep Learning Methods' from the same publication venue as 'Dynamic Selection Techniques for Detecting GPS Spoofing Attacks on UAVs'? Please answer 'Yes' or 'No'.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 16, "goal": "Are the articles 'Road Traffic Forecast Based on Meteorological Information through Deep Learning Methods' and 'Dynamic Selection Techniques for Detecting GPS Spoofing Attacks on UAVs' published in the same venue and in the same year? Please answer 'Yes' or 'No'.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 17, "goal": "Are 'Sliding Mode FTC of an Octoplane UAV transition mode' and 'Panoptic Visual Analytics of Eye Tracking Data' both conference papers? Please answer 'Yes' or 'No'.", "subgoals": ["Yes"], "additional_info": {"answer": "Yes", "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "easy"}
{"task": "tool-query", "id": 18, "goal": "Who are the collaborators of Jay Leverett from the same organization as him? Please provide the names in the following format: ['author1', 'author2', ...]", "subgoals": [["Aayush Prakash", "Daeil Kim"]], "additional_info": {"answer": ["Daeil Kim", "Aayush Prakash"], "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}
{"task": "tool-query", "id": 19, "goal": "Of Fred Zhang's collaborators, who are from different organizations than Fred Zhang? Please provide a list in the format: ['author1', 'author2', ...].", "subgoals": [["Ali Vakilian", "Justin Y. Chen", "Sandeep Silwal_3"]], "additional_info": {"answer": ["Justin Y. Chen", "Sandeep Silwal_3", "Ali Vakilian"], "init_config": null, "goal_type": 0, "tool": "academia"}, "difficulty": "hard"}