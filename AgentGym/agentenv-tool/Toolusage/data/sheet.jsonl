{"task": "tool-operation", "id": 0, "goal": "Product Update: The table in \"Sheet1\" contains the product inventory information, and [['Product', 'Today Sold'], ['beef', '5'], ['pork', '2'], ['chicken', '8'], ['lamb', '12'], ['duck', '3'], ['fish', '23'], ['shrimp', '21'], ['salmon', '12'], ['apple', '100'], ['banana', '287'], ['orange', '234'], ['carrot', '12']] is today's sales data. Please update the product information in \"Sheet1\" in time and then sort by \"Quantity\" in descending order.", "subgoals": "Reference Action Path: \nAction 1: update_range with Action Input: {'start_position': 'D2', 'end_position': 'D13', 'values_list': '[[8], [5], [10], [10], [7], [66], [21], [3], [20], [-87], [-84], [68]]'}\nAction 2: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Quantity\", \"Discount\", \"Location\", \"Product Rating\"], [\"carrot\", \"Vegetables\", \"0.40\", \"68.00\", \"0.00\", \"Store D\", \"4.2\"], [\"fish\", \"Seafood\", \"2.80\", \"66.00\", \"0.08\", \"Store B\", \"4.1\"], [\"shrimp\", \"Seafood\", \"5.40\", \"21.00\", \"0.10\", \"Store C\", \"4.4\"], [\"apple\", \"Fruits\", \"1.20\", \"20.00\", \"0.00\", \"Store A\", \"4.3\"], [\"chicken\", \"Meat\", \"9.70\", \"10.00\", \"0.15\", \"Store C\", \"4.7\"], [\"lamb\", \"Meat\", \"6.10\", \"10.00\", \"0.20\", \"Store D\", \"4.3\"], [\"beef\", \"Meat\", \"8.50\", \"8.00\", \"0.10\", \"Store A\", \"4.5\"], [\"duck\", \"Meat\", \"14.30\", \"7.00\", \"0.12\", \"Store A\", \"4.6\"], [\"pork\", \"Meat\", \"3.20\", \"5.00\", \"0.05\", \"Store B\", \"4.2\"], [\"salmon\", \"Seafood\", \"12.70\", \"3.00\", \"0.05\", \"Store D\", \"4.2\"], [\"orange\", \"Fruits\", \"0.80\", \"-84.00\", \"0.00\", \"Store C\", \"4.6\"], [\"banana\", \"Fruits\", \"0.50\", \"-87.00\", \"0.00\", \"Store B\", \"4.1\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 1, "goal": "Product Promotion: \"Sheet2\" contains the product inventory information, please sort the products in descending order by product ratings, and then, increase the discount rate of the three products with the lowest ratings by 5 percentage points.", "subgoals": "Reference Action Path: \nAction 1: sort_sheet_by_col with Action Input: {'col_num': '7', 'order': 'des'}\nAction 2: update_range with Action Input: {'start_position': 'E11', 'end_position': 'E13', 'values_list': '[[0.05], [0.13], [0.05]]'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Quantity\", \"Discount\", \"Location\", \"Product Rating\"], [\"chicken\", \"Meat\", \"9.70\", \"18.00\", \"0.15\", \"Store C\", \"4.70\"], [\"duck\", \"Meat\", \"14.30\", \"10.00\", \"0.12\", \"Store A\", \"4.60\"], [\"orange\", \"Fruits\", \"0.80\", \"150.00\", \"0.00\", \"Store C\", \"4.60\"], [\"beef\", \"Meat\", \"8.50\", \"13.00\", \"0.10\", \"Store A\", \"4.50\"], [\"shrimp\", \"Seafood\", \"5.40\", \"42.00\", \"0.10\", \"Store C\", \"4.40\"], [\"lamb\", \"Meat\", \"6.10\", \"22.00\", \"0.20\", \"Store D\", \"4.30\"], [\"apple\", \"Fruits\", \"1.20\", \"120.00\", \"0.00\", \"Store A\", \"4.30\"], [\"pork\", \"Meat\", \"3.20\", \"7.00\", \"0.05\", \"Store B\", \"4.20\"], [\"salmon\", \"Seafood\", \"12.70\", \"15.00\", \"0.05\", \"Store D\", \"4.20\"], [\"carrot\", \"Vegetables\", \"0.40\", \"80.00\", \"0.05\", \"Store D\", \"4.20\"], [\"fish\", \"Seafood\", \"2.80\", \"89.00\", \"0.13\", \"Store B\", \"4.10\"], [\"banana\", \"Fruits\", \"0.50\", \"200.00\", \"0.05\", \"Store B\", \"4.10\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 2, "goal": " Product Filtering: The table in \"Sheet3\" contains the product inventory information, and now you need to filter out the products that are neither in the 'Vegetables' nor 'Fruits' categories , and have a price greater than 5. Then, sort the filtered products in descending order by \"Quantity\".", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': \"re.compile('^(Vegetables|Fruits)$')\"}\nAction 2: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[10, 11, 12, 13]'}\nAction 3: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[3, 7]'}\nAction 4: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Quantity\", \"Discount\", \"Location\", \"Product Rating\"], [\"shrimp\", \"Seafood\", \"5.40\", \"42.00\", \"0.10\", \"Store C\", \"4.40\"], [\"lamb\", \"Meat\", \"6.10\", \"22.00\", \"0.20\", \"Store D\", \"4.30\"], [\"chicken\", \"Meat\", \"9.70\", \"18.00\", \"0.15\", \"Store C\", \"4.70\"], [\"salmon\", \"Seafood\", \"12.70\", \"15.00\", \"0.05\", \"Store D\", \"4.20\"], [\"beef\", \"Meat\", \"8.50\", \"13.00\", \"0.10\", \"Store A\", \"4.50\"], [\"duck\", \"Meat\", \"14.30\", \"10.00\", \"0.12\", \"Store A\", \"4.60\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 3, "goal": "In \"Sheet4\", delete the empty rows and columns, then merge the titles of first and second column into a single cell", "subgoals": "Reference Action Path: \nAction 1: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[8]'}\nAction 2: delete_batch_data with Action Input: {'dimension': 'col', 'index_list': '[3]'}\nAction 3: merge_cells with Action Input: {'start_position': 'A1', 'end_position': 'B1'}\n", "additional_info": {"answer": "[[\"Product\", \"Product\", \"Price\", \"Quantity\", \"Discount\", \"Location\", \"Product Rating\"], [\"Meat\", \"beef\", \"8.50\", \"13.00\", \"0.10\", \"Store A\", \"4.50\"], [\"Meat\", \"pork\", \"3.20\", \"7.00\", \"0.05\", \"Store B\", \"4.20\"], [\"Meat\", \"chicken\", \"9.70\", \"18.00\", \"0.15\", \"Store C\", \"4.70\"], [\"Meat\", \"lamb\", \"6.10\", \"22.00\", \"0.20\", \"Store D\", \"4.30\"], [\"Meat\", \"duck\", \"14.30\", \"10.00\", \"0.12\", \"Store A\", \"4.60\"], [\"Seafood\", \"fish\", \"2.80\", \"89.00\", \"0.08\", \"Store B\", \"4.10\"], [\"Seafood\", \"shrimp\", \"5.40\", \"42.00\", \"0.10\", \"Store C\", \"4.40\"], [\"Seafood\", \"salmon\", \"12.70\", \"15.00\", \"0.05\", \"Store D\", \"4.20\"], [\"Fruits\", \"apple\", \"1.20\", \"120.00\", \"0.00\", \"Store A\", \"4.30\"], [\"Fruits\", \"banana\", \"0.50\", \"200.00\", \"0.00\", \"Store B\", \"4.10\"], [\"Fruits\", \"orange\", \"0.80\", \"150.00\", \"0.00\", \"Store C\", \"4.60\"], [\"Vegetables\", \"carrot\", \"0.40\", \"80.00\", \"0.00\", \"Store D\", \"4.20\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 4, "goal": "In \"Sheet5\", please update the data according to the notes on cells: \"James Harris\", \"Matthew Thompson\", \"Sarah Johnson\" and \"Christopher Anderson\", and then sort the data in descending order by overall score.", "subgoals": "Reference Action Path: \nAction 1: get_note with Action Input: {'position': 'A4'}\nAction 2: get_note with Action Input: {'position': 'A8'}\nAction 3: get_note with Action Input: {'position': 'A11'}\nAction 4: get_note with Action Input: {'position': 'A15'}\nAction 5: update_cell with Action Input: {'position': 'C4', 'value': 'Chapter 3'}\nAction 6: update_cell with Action Input: {'position': 'C8', 'value': 'Chapter 8'}\nAction 7: update_cell with Action Input: {'position': 'C11', 'value': 'Chapter 4'}\nAction 8: update_cell with Action Input: {'position': 'C15', 'value': 'Chapter 5'}\nAction 9: update_cell with Action Input: {'position': 'D4', 'value': '45'}\nAction 10: update_cell with Action Input: {'position': 'D8', 'value': '85'}\nAction 11: update_cell with Action Input: {'position': 'D11', 'value': '62'}\nAction 12: update_cell with Action Input: {'position': 'D15', 'value': '57'}\nAction 13: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Group Number\", \"Progress\", \"Overall Score\"], [\"Emma Thompson\", \"Group 1\", \"Chapter 10\", \"90\"], [\"Sophia Thomas\", \"Group 2\", \"Chapter 6\", \"89\"], [\"Isabella Robinson\", \"Group 2\", \"Chapter 10\", \"86\"], [\"James Harris\", \"Group 3\", \"Chapter 8\", \"85\"], [\"Andrew Martin\", \"Group 5\", \"Chapter 9\", \"83\"], [\"Samantha Martinez\", \"Group 4\", \"Chapter 4\", \"80\"], [\"Lucas Garcia\", \"Group 4\", \"Chapter 5\", \"79\"], [\"Ethan Johnson\", \"Group 2\", \"Chapter 9\", \"77\"], [\"Chloe Jackson\", \"Group 4\", \"Chapter 8\", \"76\"], [\"Jessica Wilson\", \"Group 5\", \"Chapter 3\", \"65\"], [\"Matthew Thompson\", \"Group 4\", \"Chapter 4\", \"62\"], [\"Sarah Johnson\", \"Group 5\", \"Chapter 5\", \"57\"], [\"Olivia Garcia\", \"Group 3\", \"Chapter 3\", \"55\"], [\"Christopher Anderson\", \"Group 1\", \"Chapter 3\", \"45\"], [\"Michael Brown\", \"Group 2\", \"Chapter 4\", \"35\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 5, "goal": "In \"Sheet6\", calculate the sum and average scores, then enter the results in the appropriate cells in the table. Afterward, sort the table in descending order based on the sum score.", "subgoals": "Reference Action Path: \nAction 1: update_cell_by_formula with Action Input: {'start_position': 'C2', 'end_position': 'G2', 'result_position': 'H2', 'operator': 'SUM'}\nAction 2: update_cell_by_formula with Action Input: {'start_position': 'C2', 'end_position': 'G2', 'result_position': 'I2', 'operator': 'AVERAGE'}\nAction 3: update_cell_by_formula with Action Input: {'start_position': 'C3', 'end_position': 'G3', 'result_position': 'H3', 'operator': 'SUM'}\nAction 4: update_cell_by_formula with Action Input: {'start_position': 'C3', 'end_position': 'G3', 'result_position': 'I3', 'operator': 'AVERAGE'}\nAction 5: update_cell_by_formula with Action Input: {'start_position': 'C4', 'end_position': 'G4', 'result_position': 'H4', 'operator': 'SUM'}\nAction 6: update_cell_by_formula with Action Input: {'start_position': 'C4', 'end_position': 'G4', 'result_position': 'I4', 'operator': 'AVERAGE'}\nAction 7: update_cell_by_formula with Action Input: {'start_position': 'C5', 'end_position': 'G5', 'result_position': 'H5', 'operator': 'SUM'}\nAction 8: update_cell_by_formula with Action Input: {'start_position': 'C5', 'end_position': 'G5', 'result_position': 'I5', 'operator': 'AVERAGE'}\nAction 9: update_cell_by_formula with Action Input: {'start_position': 'C6', 'end_position': 'G6', 'result_position': 'H6', 'operator': 'SUM'}\nAction 10: update_cell_by_formula with Action Input: {'start_position': 'C6', 'end_position': 'G6', 'result_position': 'I6', 'operator': 'AVERAGE'}\nAction 11: update_cell_by_formula with Action Input: {'start_position': 'C7', 'end_position': 'G7', 'result_position': 'H7', 'operator': 'SUM'}\nAction 12: update_cell_by_formula with Action Input: {'start_position': 'C7', 'end_position': 'G7', 'result_position': 'I7', 'operator': 'AVERAGE'}\nAction 13: update_cell_by_formula with Action Input: {'start_position': 'C8', 'end_position': 'G8', 'result_position': 'H8', 'operator': 'SUM'}\nAction 14: update_cell_by_formula with Action Input: {'start_position': 'C8', 'end_position': 'G8', 'result_position': 'I8', 'operator': 'AVERAGE'}\nAction 15: update_cell_by_formula with Action Input: {'start_position': 'C9', 'end_position': 'G9', 'result_position': 'H9', 'operator': 'SUM'}\nAction 16: update_cell_by_formula with Action Input: {'start_position': 'C9', 'end_position': 'G9', 'result_position': 'I9', 'operator': 'AVERAGE'}\nAction 17: update_cell_by_formula with Action Input: {'start_position': 'C10', 'end_position': 'G10', 'result_position': 'H10', 'operator': 'SUM'}\nAction 18: update_cell_by_formula with Action Input: {'start_position': 'C10', 'end_position': 'G10', 'result_position': 'I10', 'operator': 'AVERAGE'}\nAction 19: update_cell_by_formula with Action Input: {'start_position': 'C11', 'end_position': 'G11', 'result_position': 'H11', 'operator': 'SUM'}\nAction 20: update_cell_by_formula with Action Input: {'start_position': 'C11', 'end_position': 'G11', 'result_position': 'I11', 'operator': 'AVERAGE'}\nAction 21: update_cell_by_formula with Action Input: {'start_position': 'C12', 'end_position': 'G12', 'result_position': 'H12', 'operator': 'SUM'}\nAction 22: update_cell_by_formula with Action Input: {'start_position': 'C12', 'end_position': 'G12', 'result_position': 'I12', 'operator': 'AVERAGE'}\nAction 23: update_cell_by_formula with Action Input: {'start_position': 'C13', 'end_position': 'G13', 'result_position': 'H13', 'operator': 'SUM'}\nAction 24: update_cell_by_formula with Action Input: {'start_position': 'C13', 'end_position': 'G13', 'result_position': 'I13', 'operator': 'AVERAGE'}\nAction 25: update_cell_by_formula with Action Input: {'start_position': 'C14', 'end_position': 'G14', 'result_position': 'H14', 'operator': 'SUM'}\nAction 26: update_cell_by_formula with Action Input: {'start_position': 'C14', 'end_position': 'G14', 'result_position': 'I14', 'operator': 'AVERAGE'}\nAction 27: update_cell_by_formula with Action Input: {'start_position': 'C15', 'end_position': 'G15', 'result_position': 'H15', 'operator': 'SUM'}\nAction 28: update_cell_by_formula with Action Input: {'start_position': 'C15', 'end_position': 'G15', 'result_position': 'I15', 'operator': 'AVERAGE'}\nAction 29: sort_sheet_by_col with Action Input: {'col_num': '8', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\", \"Biology\", \"Overall\", \"Avg\"], [\"Jack\", \"1\", \"95.0\", \"88.0\", \"92.0\", \"76.0\", \"93.0\", \"444.0\", \"88.8\"], [\"Hugo\", \"1\", \"90.0\", \"79.0\", \"85.0\", \"91.0\", \"92.0\", \"437.0\", \"87.4\"], [\"Mandy\", \"1\", \"85.0\", \"92.0\", \"79.0\", \"88.0\", \"91.0\", \"435.0\", \"87.0\"], [\"David\", \"1\", \"77.0\", \"93.0\", \"93.0\", \"85.0\", \"82.0\", \"430.0\", \"86.0\"], [\"Grace\", \"1\", \"93.0\", \"78.0\", \"89.0\", \"82.0\", \"86.0\", \"428.0\", \"85.6\"], [\"Nelson\", \"1\", \"90.0\", \"83.0\", \"94.0\", \"79.0\", \"80.0\", \"426.0\", \"85.2\"], [\"Eva\", \"1\", \"80.0\", \"82.0\", \"91.0\", \"84.0\", \"88.0\", \"425.0\", \"85.0\"], [\"Ivy\", \"1\", \"87.0\", \"96.0\", \"80.0\", \"87.0\", \"74.0\", \"424.0\", \"84.8\"], [\"Bob\", \"1\", \"90.0\", \"74.0\", \"86.0\", \"83.0\", \"90.0\", \"423.0\", \"84.6\"], [\"Leo\", \"1\", \"85.0\", \"87.0\", \"90.0\", \"75.0\", \"85.0\", \"422.0\", \"84.4\"], [\"Alice\", \"1\", \"78.0\", \"85.0\", \"88.0\", \"75.0\", \"95.0\", \"421.0\", \"84.2\"], [\"Katie\", \"1\", \"78.0\", \"94.0\", \"85.0\", \"80.0\", \"79.0\", \"416.0\", \"83.2\"], [\"Catherine\", \"1\", \"83.0\", \"80.0\", \"75.0\", \"76.0\", \"97.0\", \"411.0\", \"82.2\"], [\"Frank\", \"1\", \"89.0\", \"90.0\", \"77.0\", \"76.0\", \"75.0\", \"407.0\", \"81.4\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 6, "goal": "In \"Sheet7\", rank sheet by Biology score, and insert the column \"Biology_Rank\" after column \"Biology\", filling in the rank of each student's Biology score ( starting from 1).", "subgoals": "Reference Action Path: \nAction 1: sort_sheet_by_col with Action Input: {'col_num': '7', 'order': 'des'}\nAction 2: update_range with Action Input: {'start_position': 'H2', 'end_position': 'H26', 'values_list': '[[1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12], [13], [14], [15], [16], [17], [18], [19], [20], [21], [22], [23], [24], [25]]'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\", \"Biology\", \"Biology_Rank\"], [\"Xavier\", \"1\", \"85.0\", \"80.0\", \"85.0\", \"91.0\", \"100.0\", \"1.0\"], [\"Catherine\", \"1\", \"83.0\", \"91.0\", \"75.0\", \"76.0\", \"97.0\", \"2.0\"], [\"Robert\", \"1\", \"77.0\", \"88.0\", \"93.0\", \"77.0\", \"96.0\", \"3.0\"], [\"Alice\", \"1\", \"78.0\", \"85.0\", \"88.0\", \"77.0\", \"95.0\", \"4.0\"], [\"Yasmine\", \"1\", \"88.0\", \"87.0\", \"86.0\", \"78.0\", \"94.0\", \"5.0\"], [\"Jack\", \"1\", \"75.0\", \"88.0\", \"92.0\", \"76.0\", \"93.0\", \"6.0\"], [\"Hugo\", \"1\", \"90.0\", \"79.0\", \"85.0\", \"91.0\", \"92.0\", \"7.0\"], [\"Mandy\", \"1\", \"85.0\", \"92.0\", \"79.0\", \"88.0\", \"91.0\", \"8.0\"], [\"Bob\", \"1\", \"92.0\", \"74.0\", \"86.0\", \"83.0\", \"90.0\", \"9.0\"], [\"Wendy\", \"1\", \"78.0\", \"91.0\", \"80.0\", \"83.0\", \"89.0\", \"10.0\"], [\"Eva\", \"1\", \"80.0\", \"82.0\", \"91.0\", \"84.0\", \"88.0\", \"11.0\"], [\"Victor\", \"1\", \"86.0\", \"82.0\", \"87.0\", \"92.0\", \"87.0\", \"12.0\"], [\"Grace\", \"1\", \"93.0\", \"78.0\", \"89.0\", \"80.0\", \"86.0\", \"13.0\"], [\"Leo\", \"1\", \"86.0\", \"89.0\", \"90.0\", \"75.0\", \"85.0\", \"14.0\"], [\"David\", \"1\", \"77.0\", \"88.0\", \"93.0\", \"85.0\", \"82.0\", \"15.0\"], [\"Paul\", \"1\", \"83.0\", \"78.0\", \"86.0\", \"85.0\", \"81.0\", \"16.0\"], [\"Nelson\", \"1\", \"90.0\", \"83.0\", \"94.0\", \"79.0\", \"80.0\", \"17.0\"], [\"Katie\", \"1\", \"78.0\", \"94.0\", \"85.0\", \"80.0\", \"79.0\", \"18.0\"], [\"Sophia\", \"1\", \"88.0\", \"86.0\", \"91.0\", \"86.0\", \"78.0\", \"19.0\"], [\"Olivia\", \"1\", \"92.0\", \"74.0\", \"88.0\", \"83.0\", \"77.0\", \"20.0\"], [\"Ursula\", \"1\", \"82.0\", \"95.0\", \"77.0\", \"85.0\", \"76.0\", \"21.0\"], [\"Frank\", \"1\", \"89.0\", \"90.0\", \"77.0\", \"76.0\", \"75.0\", \"22.0\"], [\"Tom\", \"1\", \"91.0\", \"84.0\", \"78.0\", \"90.0\", \"74.0\", \"23.0\"], [\"Ivy\", \"1\", \"88.0\", \"96.0\", \"80.0\", \"87.0\", \"73.0\", \"24.0\"], [\"Quincy\", \"1\", \"84.0\", \"90.0\", \"75.0\", \"82.0\", \"72.0\", \"25.0\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 7, "goal": "In \"Sheet8\", filter out the records with incorrect scores and delete the records of the corresponding students.", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': '-1'}\nAction 2: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[2, 4, 6, 7, 10, 13]'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\", \"Biology\"], [\"Hugo\", \"1\", \"90\", \"79\", \"85\", \"91\", \"92\"], [\"Jack\", \"1\", \"75\", \"88\", \"92\", \"76\", \"93\"], [\"Paul\", \"1\", \"83\", \"78\", \"86\", \"85\", \"81\"], [\"Quincy\", \"1\", \"84\", \"90\", \"75\", \"82\", \"79\"], [\"Sophia\", \"1\", \"88\", \"86\", \"91\", \"86\", \"78\"], [\"Tom\", \"1\", \"91\", \"84\", \"78\", \"90\", \"74\"], [\"Victor\", \"1\", \"86\", \"82\", \"87\", \"92\", \"88\"], [\"Wendy\", \"1\", \"78\", \"91\", \"80\", \"83\", \"90\"], [\"Xavier\", \"1\", \"85\", \"80\", \"85\", \"91\", \"92\"], [\"Yasmine\", \"1\", \"88\", \"87\", \"86\", \"78\", \"94\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 8, "goal": "Insert \"Nelson 1 99 75 80 79\" and \"Robert 1 63 75 92 72\" into the \"Sheet9\" and sort this table by \"Name\" in ascending order.", "subgoals": "Reference Action Path: \nAction 1: insert_rows with Action Input: {'values_list': \"[['Nelson', 1, 99, 75, 80, 79], ['Robert', 1, 63, 75, 92, 72]]\", 'row_idx': '2'}\nAction 2: freeze_data with Action Input: {'dimension': 'rows', 'num': '1'}\nAction 3: sort_sheet_by_col with Action Input: {'col_num': '1', 'order': 'asc'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\"], [\"Katie\", \"1\", \"75\", \"77\", \"79\", \"80\"], [\"Leo\", \"1\", \"73\", \"87\", \"85\", \"75\"], [\"Mandy\", \"1\", \"91\", \"98\", \"91\", \"88\"], [\"Nelson\", \"1\", \"99\", \"75\", \"80\", \"79\"], [\"Olivia\", \"1\", \"98\", \"97\", \"77\", \"83\"], [\"Paul\", \"1\", \"91\", \"93\", \"81\", \"85\"], [\"Quincy\", \"1\", \"87\", \"99\", \"79\", \"82\"], [\"Robert\", \"1\", \"63\", \"75\", \"92\", \"72\"], [\"Sophia\", \"1\", \"90\", \"88\", \"78\", \"86\"], [\"Tom\", \"1\", \"83\", \"94\", \"74\", \"90\"], [\"Ursula\", \"1\", \"76\", \"92\", \"76\", \"85\"], [\"Victor\", \"1\", \"89\", \"77\", \"88\", \"92\"], [\"Wendy\", \"1\", \"74\", \"86\", \"90\", \"83\"], [\"Xavier\", \"1\", \"99\", \"79\", \"92\", \"91\"], [\"Yasmine\", \"1\", \"63\", \"78\", \"94\", \"78\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 9, "goal": "In \"Sheet10\", filter out the class with the most students and delete the data of other classes, then sort the class in descending order by Chemistry scores.", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': '1', 'in_column': '2'}\nAction 2: filter_cells with Action Input: {'query': '2', 'in_column': '2'}\nAction 3: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[3, 5, 6, 7, 9, 11, 12, 14, 17, 19, 20, 22, 23]'}\nAction 4: sort_sheet_by_col with Action Input: {'col_num': '6', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\"], [\"Victor\", \"1\", \"89\", \"77\", \"88\", \"92\"], [\"Ivy\", \"1\", \"89\", \"89\", \"74\", \"87\"], [\"Sophia\", \"1\", \"90\", \"88\", \"78\", \"86\"], [\"Olivia\", \"1\", \"98\", \"97\", \"77\", \"83\"], [\"Grace\", \"1\", \"76\", \"89\", \"86\", \"80\"], [\"Yasmine\", \"1\", \"63\", \"78\", \"94\", \"78\"], [\"Alice\", \"1\", \"70\", \"78\", \"95\", \"77\"], [\"Catherine\", \"1\", \"78\", \"87\", \"97\", \"76\"], [\"Leo\", \"1\", \"73\", \"87\", \"85\", \"75\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 10, "goal": "In the task assignment table \"Sheet11\", find and delete the tasks with the status \"Completed\". Then, sort the table in descending order by \"Estimated Hours\".", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': 'Completed', 'in_column': '3'}\nAction 2: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[3, 10, 13]'}\nAction 3: sort_sheet_by_col with Action Input: {'col_num': '5', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Task Name\", \"Responsible Person\", \"Status\", \"Priority\", \"Estimated Hours\"], [\"App Development\", \"Susan Clark\", \"Not Started\", \"Medium\", \"25\"], [\"Content Creation\", \"Mary Johnson\", \"In Progress\", \"High\", \"20\"], [\"Market Research\", \"Elizabeth Davis\", \"In Progress\", \"Low\", \"15\"], [\"Security Audit\", \"Steven Harris\", \"In Progress\", \"High\", \"11\"], [\"Website Update\", \"John Doe\", \"In Progress\", \"High\", \"10\"], [\"SEO Optimization\", \"Peter Williams\", \"Not Started\", \"Medium\", \"8\"], [\"Newsletter Preparation\", \"Patricia Anderson\", \"In Progress\", \"Medium\", \"7\"], [\"Hardware Upgrade\", \"Barbara White\", \"Not Started\", \"Medium\", \"6\"], [\"Client Meeting\", \"Emily Taylor\", \"Scheduled\", \"High\", \"4\"], [\"Server Maintenance\", \"John Doe\", \"Scheduled\", \"Low\", \"3\"], [\"Budget Review\", \"Michael Brown\", \"Not Started\", \"Medium\", \"3\"], [\"Social Media Update\", \"Robert Thomas\", \"Scheduled\", \"Low\", \"2\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 11, "goal": "In \"Sheet12\", you need to update the grades of the following students: Quincy, Tom, Grace and Leo, please check the note in the cell under their names to complete the updating of the subject grades and sums. Lastly, sort the table in descending order by 'English score'.", "subgoals": "Reference Action Path: \nAction 1: get_note with Action Input: {'position': 'A5'}\nAction 2: update_cell with Action Input: {'position': 'E5', 'value': '82'}\nAction 3: update_cell_by_formula with Action Input: {'start_position': 'C5', 'end_position': 'F5', 'result_position': 'G5', 'operator': 'SUM'}\nAction 4: get_note with Action Input: {'position': 'A9'}\nAction 5: update_cell with Action Input: {'position': 'C9', 'value': '86'}\nAction 6: update_cell_by_formula with Action Input: {'start_position': 'C9', 'end_position': 'F9', 'result_position': 'G9', 'operator': 'SUM'}\nAction 7: get_note with Action Input: {'position': 'A13'}\nAction 8: update_cell with Action Input: {'position': 'C13', 'value': '84'}\nAction 9: update_cell_by_formula with Action Input: {'start_position': 'C13', 'end_position': 'F13', 'result_position': 'G13', 'operator': 'SUM'}\nAction 10: get_note with Action Input: {'position': 'A15'}\nAction 11: update_cell with Action Input: {'position': 'F15', 'value': '83'}\nAction 12: update_cell_by_formula with Action Input: {'start_position': 'C15', 'end_position': 'F15', 'result_position': 'G15', 'operator': 'SUM'}\nAction 13: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\", \"Sum\"], [\"Quincy\", \"1\", \"87\", \"99\", \"82\", \"82\", \"350\"], [\"Mandy\", \"2\", \"91\", \"98\", \"91\", \"88\", \"368\"], [\"Olivia\", \"1\", \"98\", \"97\", \"77\", \"83\", \"355\"], [\"Tom\", \"2\", \"86\", \"94\", \"74\", \"90\", \"344\"], [\"Paul\", \"2\", \"91\", \"93\", \"81\", \"85\", \"350\"], [\"Ursula\", \"2\", \"76\", \"92\", \"76\", \"85\", \"329\"], [\"Jack\", \"2\", \"87\", \"89\", \"93\", \"76\", \"345\"], [\"Ivy\", \"1\", \"89\", \"89\", \"72\", \"87\", \"339\"], [\"Grace\", \"1\", \"84\", \"89\", \"86\", \"80\", \"339\"], [\"Sophia\", \"1\", \"90\", \"88\", \"78\", \"86\", \"342\"], [\"Hugo\", \"2\", \"61\", \"87\", \"92\", \"91\", \"331\"], [\"Leo\", \"1\", \"73\", \"87\", \"85\", \"83\", \"328\"], [\"Wendy\", \"2\", \"74\", \"86\", \"90\", \"83\", \"333\"], [\"Yasmine\", \"1\", \"63\", \"78\", \"94\", \"78\", \"313\"], [\"Victor\", \"1\", \"89\", \"77\", \"88\", \"92\", \"346\"], [\"Katie\", \"2\", \"75\", \"77\", \"79\", \"80\", \"311\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 12, "goal": "In \"Sheet13\", update the information for the task with the following: [['Task Name', 'Status', 'Time required'], ['Database Backup', 'In Progress', '2'], ['Client Meeting', 'Completed', '0'], ['Newsletter Preparation', 'In Progress', '3'], ['Security Audit', 'In Progress', '5']]. After updating, sort the table by \"Time required\" in descending order", "subgoals": "Reference Action Path: \nAction 1: update_range with Action Input: {'start_position': 'A3', 'end_position': 'C3', 'values_list': \"[['Database Backup', 'In Progress', '2']]\"}\nAction 2: update_range with Action Input: {'start_position': 'A7', 'end_position': 'C7', 'values_list': \"[['Client Meeting', 'Completed', '0']]\"}\nAction 3: update_range with Action Input: {'start_position': 'A10', 'end_position': 'C10', 'values_list': \"[['Newsletter Preparation', 'In Progress', '3']]\"}\nAction 4: update_range with Action Input: {'start_position': 'A13', 'end_position': 'C13', 'values_list': \"[['Security Audit', 'In Progress', '5']]\"}\nAction 5: sort_sheet_by_col with Action Input: {'col_num': '3', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Task Name\", \"Status\", \"Time required\", \"Category\"], [\"Content Creation\", \"In Progress\", \"20\", \"Marketing\"], [\"Market Research\", \"In Progress\", \"15\", \"Marketing\"], [\"Website Update\", \"In Progress\", \"10\", \"Development\"], [\"SEO Optimization\", \"Not Started\", \"8\", \"Marketing\"], [\"Graphic Design\", \"Completed\", \"7\", \"Design\"], [\"Hardware Upgrade\", \"Not Started\", \"6\", \"IT\"], [\"Security Audit\", \"In Progress\", \"5\", \"IT\"], [\"Server Maintenance\", \"Scheduled\", \"4\", \"Maintenance\"], [\"Newsletter Preparation\", \"In Progress\", \"3\", \"Marketing\"], [\"Database Backup\", \"In Progress\", \"2\", \"Maintenance\"], [\"Social Media Update\", \"Scheduled\", \"1\", \"Marketing\"], [\"Client Meeting\", \"Completed\", \"0\", \"Client Relations\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 13, "goal": "In \"Sheet14\", complete the table with the missing elements based on the information provided : [['Maintenance', 'Marketing', 'Finance'], ['Jane Smith', 'James Wilson', 'Linda Jackson']]. Then, sort the table in descending order by \"Time required\".", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': 'None', 'in_column': '2'}\nAction 2: update_cell with Action Input: {'position': 'B3', 'value': 'Jane Smith'}\nAction 3: update_cell with Action Input: {'position': 'B5', 'value': 'James Wilson'}\nAction 4: update_cell with Action Input: {'position': 'B7', 'value': 'Linda Jackson'}\nAction 5: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Task Name\", \"Responsible Person\", \"Priority\", \"Estimated Hours\", \"Category\"], [\"App Development\", \"Susan Clark\", \"Medium\", \"25\", \"Development\"], [\"Content Creation\", \"Mary Johnson\", \"High\", \"20\", \"Marketing\"], [\"Market Research\", \"Elizabeth Davis\", \"Low\", \"15\", \"Marketing\"], [\"Security Audit\", \"Steven Harris\", \"High\", \"11\", \"IT\"], [\"Website Update\", \"John Doe\", \"High\", \"10\", \"Development\"], [\"SEO Optimization\", \"James Wilson\", \"Medium\", \"8\", \"Marketing\"], [\"Newsletter Preparation\", \"Patricia Anderson\", \"Medium\", \"7\", \"Marketing\"], [\"Hardware Upgrade\", \"Barbara White\", \"Medium\", \"6\", \"IT\"], [\"Client Meeting\", \"Peter Williams\", \"High\", \"4\", \"Client Relations\"], [\"Server Maintenance\", \"Jane Smith\", \"Low\", \"3\", \"Maintenance\"], [\"Social Media Update\", \"Robert Thomas\", \"Low\", \"2\", \"Marketing\"], [\"Budget Review\", \"Linda Jackson\", \"Medium\", \"1\", \"Finance\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 14, "goal": "In \"Sheet15\" with the work assignment information, please sort the sheet in ascending order by \"Category\", then, calculate the total amount of time consumed for each task category, fill in the result in \"Sum Hours\". Lastly, merge the results of tasks in the same category into a single cell representing the total amount of time consumed for that category. ", "subgoals": "Reference Action Path: \nAction 1: sort_sheet_by_col with Action Input: {'col_num': '4', 'order': 'asc'}\nAction 2: merge_cells with Action Input: {'start_position': 'F4', 'end_position': 'F5'}\nAction 3: merge_cells with Action Input: {'start_position': 'F6', 'end_position': 'F7'}\nAction 4: merge_cells with Action Input: {'start_position': 'F8', 'end_position': 'F12'}\nAction 5: update_range with Action Input: {'start_position': 'F2', 'end_position': 'F3', 'values_list': '[[4], [10]]'}\nAction 6: update_cell_by_formula with Action Input: {'start_position': 'E4', 'end_position': 'E5', 'result_position': 'F4', 'operator': 'SUM'}\nAction 7: update_cell_by_formula with Action Input: {'start_position': 'E6', 'end_position': 'E7', 'result_position': 'F6', 'operator': 'SUM'}\nAction 8: update_cell_by_formula with Action Input: {'start_position': 'E8', 'end_position': 'E12', 'result_position': 'F8', 'operator': 'SUM'}\n", "additional_info": {"answer": "[[\"Task Name\", \"Status\", \"Priority\", \"Category\", \"Time required\", \"Sum Hours\"], [\"Client Meeting\", \"Scheduled\", \"High\", \"Client Relations\", \"4\", \"4\"], [\"Website Update\", \"In Progress\", \"High\", \"Development\", \"10\", \"10\"], [\"Hardware Upgrade\", \"Not Started\", \"Medium\", \"IT\", \"6\", \"17\"], [\"Security Audit\", \"In Progress\", \"High\", \"IT\", \"11\", \"17\"], [\"Database Backup\", \"Scheduled\", \"Medium\", \"Maintenance\", \"2\", \"5\"], [\"Server Maintenance\", \"Scheduled\", \"Low\", \"Maintenance\", \"3\", \"5\"], [\"Content Creation\", \"In Progress\", \"High\", \"Marketing\", \"20\", \"52\"], [\"SEO Optimization\", \"Not Started\", \"Medium\", \"Marketing\", \"8\", \"52\"], [\"Market Research\", \"In Progress\", \"Low\", \"Marketing\", \"15\", \"52\"], [\"Newsletter Preparation\", \"In Progress\", \"Medium\", \"Marketing\", \"7\", \"52\"], [\"Social Media Update\", \"Scheduled\", \"Low\", \"Marketing\", \"2\", \"52\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 15, "goal": "In \"Sheet16\", which contains product inventory information, please first update it with the following information: [['Product', ' Updated Discount'], ['banana', '0.10'], ['duck', '0.00'], ['fish', '0.05'], ['pork', '0.00'], ['shrimp' , '0.08'], ['chicken', '0.15'], ['apple', '0.12'], ['beef', '0.12']]. After that, sort the table by \"Product Rating\" in descending order.", "subgoals": "Reference Action Path: \nAction 1: update_range with Action Input: {'start_position': 'E2', 'end_position': 'E9', 'values_list': \"[['0.10'], ['0.00'], ['0.05'], ['0.00'], ['0.08'], ['0.15'], ['0.12'], ['0.12']]\"}\nAction 2: sort_sheet_by_col with Action Input: {'col_num': '7', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Quantity\", \"Discount\", \"Location\", \"Product Rating\"], [\"chicken\", \"Meat\", \"9.70\", \"10.00\", \"0.15\", \"Store C\", \"4.7\"], [\"duck\", \"Meat\", \"14.30\", \"7.00\", \"0.00\", \"Store A\", \"4.6\"], [\"beef\", \"Meat\", \"8.50\", \"8.00\", \"0.12\", \"Store A\", \"4.5\"], [\"shrimp\", \"Seafood\", \"5.40\", \"21.00\", \"0.08\", \"Store C\", \"4.4\"], [\"apple\", \"Fruits\", \"1.20\", \"20.00\", \"0.12\", \"Store A\", \"4.3\"], [\"pork\", \"Meat\", \"3.20\", \"5.00\", \"0.00\", \"Store B\", \"4.2\"], [\"banana\", \"Fruits\", \"0.50\", \"13.00\", \"0.10\", \"Store B\", \"4.1\"], [\"fish\", \"Seafood\", \"2.80\", \"66.00\", \"0.05\", \"Store B\", \"4.1\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "easy"}
{"task": "tool-operation", "id": 16, "goal": "In \"Sheet17\", calculate and complete the \"Profit\" of the products in the table based on the sales information of the products. And then, sort the table in descending order by \"Profit\".", "subgoals": "Reference Action Path: \nAction 1: update_cell_by_formula with Action Input: {'start_position': 'C2', 'end_position': 'D2', 'result_position': 'E2', 'operator': 'PRODUCT'}\nAction 2: update_cell_by_formula with Action Input: {'start_position': 'C3', 'end_position': 'D3', 'result_position': 'E3', 'operator': 'PRODUCT'}\nAction 3: update_cell_by_formula with Action Input: {'start_position': 'C4', 'end_position': 'D4', 'result_position': 'E4', 'operator': 'PRODUCT'}\nAction 4: update_cell_by_formula with Action Input: {'start_position': 'C5', 'end_position': 'D5', 'result_position': 'E5', 'operator': 'PRODUCT'}\nAction 5: update_cell_by_formula with Action Input: {'start_position': 'C6', 'end_position': 'D6', 'result_position': 'E6', 'operator': 'PRODUCT'}\nAction 6: update_cell_by_formula with Action Input: {'start_position': 'C7', 'end_position': 'D7', 'result_position': 'E7', 'operator': 'PRODUCT'}\nAction 7: update_cell_by_formula with Action Input: {'start_position': 'C8', 'end_position': 'D8', 'result_position': 'E8', 'operator': 'PRODUCT'}\nAction 8: update_cell_by_formula with Action Input: {'start_position': 'C9', 'end_position': 'D9', 'result_position': 'E9', 'operator': 'PRODUCT'}\nAction 9: sort_sheet_by_col with Action Input: {'col_num': '5', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Sold out\", \"Profit\"], [\"fish\", \"Seafood\", \"2.80\", \"66.00\", \"184.8\"], [\"shrimp\", \"Seafood\", \"5.40\", \"21.00\", \"113.4\"], [\"duck\", \"Meat\", \"14.30\", \"7.00\", \"100.1\"], [\"chicken\", \"Meat\", \"9.70\", \"10.00\", \"97\"], [\"beef\", \"Meat\", \"8.50\", \"8.00\", \"68\"], [\"apple\", \"Fruits\", \"1.20\", \"20.00\", \"24\"], [\"pork\", \"Meat\", \"3.20\", \"5.00\", \"16\"], [\"banana\", \"Fruits\", \"0.50\", \"13.00\", \"6.5\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 17, "goal": "In \"Sheet18\", remove all records for the store with the highest total profit, and then sort the table in descending order by \"Profit\".", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': 'Store C', 'in_column': '5'}\nAction 2: get_value_by_formula with Action Input: {'position_list': \"['F2', 'F9']\", 'operator': 'SUM'}\nAction 3: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[2, 9]'}\nAction 4: sort_sheet_by_col with Action Input: {'col_num': '6', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Product\", \"Category\", \"Price\", \"Quantity\", \"Location\", \"Profit\"], [\"fish\", \"Seafood\", \"2.80\", \"66.00\", \"Store B\", \"184.8\"], [\"duck\", \"Meat\", \"14.30\", \"7.00\", \"Store A\", \"100.1\"], [\"beef\", \"Meat\", \"8.50\", \"8.00\", \"Store A\", \"68\"], [\"apple\", \"Fruits\", \"1.20\", \"20.00\", \"Store A\", \"24\"], [\"pork\", \"Meat\", \"3.20\", \"5.00\", \"Store B\", \"16\"], [\"banana\", \"Fruits\", \"0.50\", \"13.00\", \"Store B\", \"6.5\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 18, "goal": "In \"Sheet19\", filter out records with \"math\" greater than 80 and \"Chemistry\" greater than 85, delete the other records. Lastly, sort by \"Chemistry scores\" in descending order.", "subgoals": "Reference Action Path: \nAction 1: filter_cells with Action Input: {'query': \"re.compile('8[1-9]|[9]\\\\\\\\d|\\\\\\\\d{3,}')\", 'in_column': '3'}\nAction 2: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[2, 5, 6, 12]'}\nAction 3: filter_cells with Action Input: {'query': \"re.compile('8[6-9]|[9]\\\\\\\\d|\\\\\\\\d{3,}')\", 'in_column': '6'}\nAction 4: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[2, 3, 4, 5, 8, 9, 11]'}\nAction 5: sort_sheet_by_col with Action Input: {'col_num': '6', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Class\", \"Math\", \"English\", \"Physics\", \"Chemistry\", \"Biology\"], [\"Hugo\", \"1\", \"90\", \"79\", \"85\", \"91\", \"92\"], [\"Mandy\", \"1\", \"85\", \"92\", \"79\", \"88\", \"91\"], [\"Ivy\", \"1\", \"87\", \"96\", \"80\", \"87\", \"74\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
{"task": "tool-operation", "id": 19, "goal": "In \"Sheet20\", filter out the highest score for each subject and delete those records. And then sort the table in descending order by \"Biology scores\".", "subgoals": "Reference Action Path: \nAction 1: get_value_by_formula with Action Input: {'start_position': 'B2', 'end_position': 'B11', 'operator': 'MAX'}\nAction 2: filter_cells with Action Input: {'query': '95.0', 'in_column': '2'}\nAction 3: get_value_by_formula with Action Input: {'start_position': 'C2', 'end_position': 'C11', 'operator': 'MAX'}\nAction 4: filter_cells with Action Input: {'query': '96.0', 'in_column': '3'}\nAction 5: get_value_by_formula with Action Input: {'start_position': 'D2', 'end_position': 'D11', 'operator': 'MAX'}\nAction 6: filter_cells with Action Input: {'query': '91.0', 'in_column': '4'}\nAction 7: filter_cells with Action Input: {'query': '97.0', 'in_column': '5'}\nAction 8: delete_batch_data with Action Input: {'dimension': 'row', 'index_list': '[11, 10, 9, 4]'}\nAction 9: sort_sheet_by_col with Action Input: {'col_num': '5', 'order': 'des'}\n", "additional_info": {"answer": "[[\"Name\", \"Math\", \"English\", \"Chemistry\", \"Biology\"], [\"Alice\", \"78.0\", \"85.0\", \"75.0\", \"95.0\"], [\"Bob\", \"90.0\", \"74.0\", \"83.0\", \"90.0\"], [\"Eva\", \"80.0\", \"82.0\", \"84.0\", \"88.0\"], [\"Grace\", \"93.0\", \"78.0\", \"82.0\", \"86.0\"], [\"David\", \"77.0\", \"93.0\", \"85.0\", \"82.0\"], [\"Frank\", \"89.0\", \"90.0\", \"76.0\", \"75.0\"]]", "init_config": null, "goal_type": 1, "tool": "sheet"}, "difficulty": "hard"}
