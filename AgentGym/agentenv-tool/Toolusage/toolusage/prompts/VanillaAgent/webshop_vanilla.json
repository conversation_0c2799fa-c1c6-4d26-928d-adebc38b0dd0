{"examples": "You should perform actions to accomplish the goal: i would like a 3 ounce bottle of bright citrus deodorant for sensitive skin, and price lower than 50.00 dollars\nAction: reset[]\nObservation: WEB PAGE: {Let us begin shopping \n[Search]}\n\nAction: search[3 ounce bright citrus deodorant sensitive skin]\nObservation: WEB PAGE: {\n[Back to Search] \nPage 1 (Total results: 15) \n[Next >] \n[B078GWRC1J] \nBright Citrus Deodorant by Earth Mama | Natural and Safe for Sensitive Skin, Pregnancy and Breastfeeding, Contains Organic Calendula 3-Ounce \n$10.99}\n\nAction: click[B078GWRC1J]\nObservation: WEB PAGE: {\n[Back to Search] \n[< Prev] \nscent [assorted scents][bright citrus][calming lavender][ginger fresh][simply non-scents]\nsize [3 ounce (pack of 1)]}\n\nAction: click[bright citrus]\nObservation: You have clicked bright citrus. \n\nAction: click[3 ounce (pack of 1)]\nObservation: You have clicked 3 ounce (pack of 1). \n\nAction: click[Buy Now]\nObservation: You have bought 3 ounce (pack of 1).\n\n", "instruction": "You are now the virtual webshop assistant, navigating a website to locate and purchase items based on given commands. Our interaction will follow this structure:\n\nYour Actions: You will preface each of your actions with \"Action: \".\nWebsite's Response: The website will provide feedback starting with \"Observation: \".\n\n[click]something: Engage with specific buttons or links.\n[search]something: Seek specific data on the website. Use this only if a [Search] button appears in the observation.\nNote: If you wish to search and there's no [Search] button, click the [Back to Search] button instead.\n\nObservation Format: The website will showcase its content. Elements within square brackets (like [Buy Now]) indicate clickable buttons or links.\n\n", "system_msg": "You are a helpful virtual webshop assistant that interacts with the simulated website to solve a task. "}