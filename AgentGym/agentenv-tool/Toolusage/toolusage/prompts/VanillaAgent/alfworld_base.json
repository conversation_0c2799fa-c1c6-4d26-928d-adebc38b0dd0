{"examples": {"put": ["Your task is to: put some spraybottle on toilet.\nYou are in the middle of a room. Looking quickly around you, you see a cabinet 4, a cabinet 3, a cabinet 2, a cabinet 1, a countertop 1, a garbagecan 1, a handtowelholder 2, a handtowelholder 1, a sinkbasin 2, a sinkbasin 1, a toilet 1, a toiletpaperhanger 1, and a towelholder 1.\n", "Action: go to cabinet 1\n", "Observation: On the cabinet 1, you see a cloth 1, a soapbar 1, a soapbottle 1.\n", "Action: go to cabinet 2\n", "Observation: The cabinet 2 is closed.\n", "Action: open cabinet 2\n", "Observation: You open the cabinet 2. The cabinet 2 is open. In it, you see a candle 1, and a spraybottle 2.\n", "Action: take spraybottle 2 from cabinet 2\n", "Observation: You pick up the spraybottle 2 from the cabinet 2.\n", "Action: go to toilet 1\n", "Observation: On the toilet 1, you see a soapbottle 2.\n", "Action: put spraybottle 2 in/on toilet 1\n", "Observation: You put the spraybottle 2 in/on the toilet 1.\n"], "clean": ["Your task is to: clean some soapbar and put it in toilet.\nYou are in the middle of a room. Looking quickly around you, you see a bathtubbasin 1, a garbagecan 1, a handtowelholder 1, a shelf 3, a shelf 2, a shelf 1, a sinkbasin 1, a toilet 1, a toiletpaperhanger 1, and a towelholder 1.\n", "Action: go to toilet 1\n", "Observation: On the toilet 1, you see a soapbar 4, and a soapbar 3.\n", "Action: take soapbar 4 from toilet 1\n", "Observation: You pick up the soapbar 4 from the toilet 1.\n", "Action: go to sinkbasin 1\n", "Observation: On the sinkbasin 1, you see nothing.\n", "Action: clean soapbar 4 with sinkbasin 1\n", "Observation: You clean the soapbar 4 using the sinkbasin 1.\n", "Action: go to toilet 1\n", "Observation: On the toilet 1, you see a soapbar 3.\n", "Action: put soapbar 4 in/on toilet 1\n", "Observation: You put the soapbar 4 in/on the toilet 1.\n"], "heat": ["Your task is to: put a hot apple in fridge.\nYou are in the middle of a room. Looking quickly around you, you see a cabinet 13, a cabinet 12, a cabinet 11, a cabinet 10, a cabinet 9, a cabinet 8, a cabinet 7, a cabinet 6, a cabinet 5, a cabinet 4, a cabinet 3, a cabinet 2, a cabinet 1, a coffeemachine 1, a countertop 1, a diningtable 1, a drawer 1, a fridge 1, a garbagecan 1, a microwave 1, a shelf 3, a shelf 2, a shelf 1, a sinkbasin 1, a stoveburner 4, a stoveburner 3, a stoveburner 2, a stoveburner 1, and a toaster 1.\n", "Action: go to diningtable 1\n", "Observation: On the diningtable 1, you see a apple 1, a bread 1, a fork 3, a fork 2, a fork 1, a mug 2, a peppershaker 3, a plate 2, a pot 1, a soapbottle 3, a spatula 1, a spoon 3, a spoon 2, a spoon 1, a winebottle 3, and a winebottle 2.\n\n", "Action: take apple 1 from diningtable 1\n", "Observation: You pick up the apple 1 from the diningtable 1.\n", "Action: go to microwave 1\n", "Observation: The microwave 1 is closed.\n", "Action: heat apple 1 with microwave 1\n", "Observation: You heat the apple 1 using the microwave 1.\n", "Action: go to fridge 1\n", "Observation: The fridge 1 is open. In it, you see a cup 1, and a egg 1.\n", "Action: put apple 1 in/on fridge 1\n", "Observation: You put the apple 1 in/on the fridge 1.\n"], "cool": ["Your task is to: cool some pan and put it in stoveburner.\nYou are in the middle of a room. Looking quickly around you, you see a cabinet 16, a cabinet 15, a cabinet 14, a cabinet 13, a cabinet 12, a cabinet 11, a cabinet 10, a cabinet 9, a cabinet 8, a cabinet 7, a cabinet 6, a cabinet 5, a cabinet 4, a cabinet 3, a cabinet 2, a cabinet 1, a coffeemachine 1, a countertop 2, a countertop 1, a diningtable 1, a drawer 5, a drawer 4, a drawer 3, a drawer 2, a drawer 1, a fridge 1, a garbagecan 1, a microwave 1, a safe 1, a sinkbasin 1, a stoveburner 4, a stoveburner 3, a stoveburner 2, a stoveburner 1, and a toaster 1.\n", "Action: go to stoveburner 1\n", "Observation: On the stoveburner 1, you see nothing.\n", "Action: go to stoveburner 2\n", "Observation: On the stoveburner 2, you see a pot 1.\n", "Action: go to stoveburner 3\n", "Observation: On the stoveburner 3, you see a pan 1.\n", "Action: take pan 1 from stoveburner 3\n", "Observation: You pick up the pan 1 from the stoveburner 3.\n", "Action: go to fridge 1\n", "Observation: The fridge 1 is closed.\n", "Action: cool pan 1 with fridge 1\n", "Observation: You cool the pan 1 using the fridge 1.\n", "Action: go to stoveburner 1\n", "Observation: On the stoveburner 1, you see nothing.\n", "Action: put pan 1 in/on stoveburner 1\n", "Observation: You put the pan 1 in/on the stoveburner 1.\n"], "puttwo": ["Your task is to: put two cellphone in sofa.\nYou are in the middle of a room. Looking quickly around you, you see a coffeetable 1, a diningtable 1, a drawer 4, a drawer 3, a drawer 2, a drawer 1, a dresser 1, a garbagecan 1, a sidetable 2, a sidetable 1, and a sofa 1.\n", "Action: go to coffeetable 1\n", "Observation: On the coffeetable 1, you see a box 1, a cellphone 3, and a plate 1.\n", "Action: take cellphone 3 from coffeetable 1\n", "Observation: You pick up the cellphone 3 from the coffeetable 1.\n", "Action: go to sofa 1\n", "Observation: On the sofa 1, you see a newspaper 2, a pillow 1, a remotecontrol 2, and a remotecontrol 1.\n", "Action: put cellphone 3 in/on sofa 1\n", "Observation: You put the cellphone 3 in/on the sofa 1.\n", "Action: go to diningtable 1\n", "Observation: On the diningtable 1, you see a cellphone 2, a keychain 2, a laptop 1, a statue 2, and a statue 1.\n", "Action: take cellphone 2 from diningtable 1\n", "Observation: You pick up the cellphone 2 from the diningtable 1.\n", "Action: go to sofa 1\n", "Observation: On the sofa 1, you see a cellphone 3, a newspaper 2, a pillow 1, a remotecontrol 2, and a remotecontrol 1.\n", "Action: put cellphone 2 in/on sofa 1\n", "Observation: You put the cellphone 2 in/on the sofa 1.\n"], "examine": ["Your task is to: look at statue under the desklamp.\nYou are in the middle of a room. Looking quickly around you, you see a coffeetable 1, a diningtable 1, a drawer 4, a drawer 3, a drawer 2, a drawer 1, a dresser 1, a garbagecan 1, a sidetable 2, a sidetable 1, and a sofa 1.\n", "Action: go to dresser 1\n", "Observation: On the dresser 1, you see a cellphone 3, a newspaper 2, a statue 1, and a television 1.\n", "Action: take statue 1 from dresser 1\n", "Observation: You pick up the statue 1 from the dresser 1.\n", "Action: go to sidetable 1\n", "Observation: On the sidetable 1, you see nothing.\n", "Action: go to sidetable 2\n", "Observation: On the sidetable 2, you see a desklamp 3, a newspaper 1, and a statue 2.\n", "Action: use desklamp 3\n", "Observation: You turn on the desklamp 3.\n"]}, "system_msg": "You are a helpful assistant.", "instruction": "Your task is to interact with a virtual household simulator to accomplish a specific task. With each interaction, you will receive an observation.\nYour role is to decide on an action based on the observation. Please ensure that any objects ('{obj}') and receptacles ('{recep}') you mention in your response are present in the observation provided.\n\nHere are the available actions you can take:\n- 'take {obj} from {recep}'\n- 'put {obj} in/on {recep}'\n- 'open {recep}'\n- 'close {recep}'\n- 'toggle {obj}/{recep}'\n- 'clean {obj} using {recep}'\n- 'cool {obj} using {recep}'\n- 'heat {obj} using {recep}'\n- 'inventory'\n- 'examine {recep}/{obj}'\n- 'go to {recep}'\n"}