{"instruction": "We detail name, description, input(parameters) and output(returns) of each action as follows:\nName: loadPaperNet()\nDescription: Load PaperNet. In this net, nodes are papers and edges are citation relationships between papers.\n\nName: loadAuthorNet()\nDescription: Load AuthorNet. In this net, nodes are authors and edges are collaboration relationships between authors.\n\nName: neighbourCheck(graph, node)\nDescription: List the first-order neighbors connect to the node. In paperNet, neigbours are cited papers of the paper. In authorNet, neigbours are collaborators of the author.\nParameters:\n- graph (Type: string, Enum: [PaperNet, AuthorNet]): The name of the graph to check\n- node (Type: string): The node for which neighbors will be listed\nReturns:\n- neighbors (Type: array)\n\nName: paperNodeCheck(node)\nDescription: Return detailed attribute information of a specified paper in PaperNet\nParameters:\n- node (Type: string): Name of the paper.\nReturns:\n- authors : The authors of the paper\n- year : The puslished year of the paper\n- venue : The published venue of the paper\n- n_citation : The number of citations of the paper\n- keywords : The keywords of the paper\n- doc_type : The document type of the paper\n\nName: author<PERSON><PERSON><PERSON><PERSON><PERSON>(node)\nDescription: Return detailed attribute information of a specified author in AuthorNet\nParameters:\n- node (Type: string): name of the author.\nReturns:\n- name : The name of the author\n- org : The organization of the author\n\nName: author<PERSON><PERSON><PERSON><PERSON><PERSON>(node1, node2)\nDescription: Return detailed attribute information of the edge between two specified nodes in a AuthorNet.\nParameters:\n- node1 (Type: string): The first node of the edge\n- node2 (Type: string): The second node of the edge\nReturns:\n- papers : All papers that the two authors have co-authored\n\nName: paperEdgeCheck(node1, node2)\nDescription: Return detailed attribute information of the edge between two specified nodes in a PaperNet.\nParameters:\n- node1 (Type: string): The first node of the edge\n- node2 (Type: string): The second node of the edge\nReturns:\nNone\n\nName: check_valid_actions()\nDescription: Get supported actions for current tool.\nReturns:\n- actions (Type: array): Supported actions for current tool.\n\nName: finish(answer)\nDescription: Return an answer and finish the task\nParameters:\n- answer (Type: ['string', 'number', 'array']): The answer to be returned\n\n\nIf you are finished, you will call \"finish\" action\nPlease refer to the format of examples below to solve the requested goal. Your response must be in the format of \"Action: [your action] with Action Input: [your action input]\"", "examples": ["Goal: When was the paper Learning the Principle of Least Action with Reinforcement Learning. published?\n\nAction: loadPaperNet with Action Input: {}\nObservation: PaperNet is loaded.\nAction: paperNodeCheck with Action Input: {\"node\":\"Learning the Principle of Least Action with Reinforcement Learning.\"}\nObservation: {'year': 2021, 'venue': 'AAAI Spring Symposium - MLPS', 'n_citation': 0, 'keywords': [], 'doc_type': 'Conference'}\nAction: finish with Action Input: {\"answer\": \"2021\"}\nObservation: 2021\n"], "system_msg": "You can use actions to help people solve problems.\n"}