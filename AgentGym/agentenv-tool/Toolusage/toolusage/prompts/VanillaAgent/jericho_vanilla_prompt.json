{"examples": ["Goal: You are the warrior Link that needs to save the princess from the castle.\nObservation: You are at the path leading to the castle. The castle is to your north. There is a barrel in front of you. \nAction: Examine barrel\nObservation: There is a gleaming elvish sword. \nAction: take the sword\nObservation: The sword is taken\nAction: north\nObservation: Main hall \n The castle is dimly lit, with lava flowing in many places. There is a staircase leading up to princess bedroom, a door to your north leading to the kitchen hall, and the door you just entered on your south\nAction: check valid actions\nObservation: south, north, up, look, examine staircase\nAction: up\nObservation: Princess’s bedroom. Princess is sleeping in bed. \nAction: check valid actions\nObservation: wake up the princess,  take out sword, down\nAction: wake up the princess\nObservation: The princess wake up from the coma. Thank you my knight, she says. The task is finished."], "instruction": "You are in a fictional game environment and you need to accomplish goals by performing actions. Each action is a simple phrase. Here are the actions you can do: \nInventory: check things you are carrying\nLook: check your surroundings\nExamine <place/obj>: check the details of something\nTake <obj>: pickup obj\n Put down <obj>: leave a obj at your current place.\n Drop <obj>\nCheck valid actions: Check actions you can use\nSouth: go south\nNorth: go north\nEast: go east\nWest: go west\nUp: go up\nDown: go down\nOther available actions could be determined through check valid actions.\n", "system_msg": "You are a game master in fictional text games."}